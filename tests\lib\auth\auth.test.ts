// tests/lib/product/product.test.ts

import sdk from "../../setup";

describe("AuthService", () => {
  let authService: any;

  beforeAll(() => {
    authService = sdk.auth;
  });

  it("login integration test", async () => {
    const response = await authService.login({
      username: "admin",
      password: "123456",
    });
    console.log(response);
    sdk.setToken(response.accessToken);
    expect(response).toHaveProperty("accessToken");
  });

  // it("register integration test", async () => {
  //   const response = await authService.register("username", "password");
  //   expect(response).toHaveProperty("success", true);
  // });

  describe("New Authentication Functions", () => {
    describe("createOrg", () => {
      it("should call graphqlMutation with correct parameters", async () => {
        const mockResponse = { createOrg: "org-created-successfully" };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.createOrg("Test Organization");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // CREATE_ORG_MUTATION
          { orgName: "Test Organization" }
        );
        expect(result).toBe("org-created-successfully");

        spy.mockRestore();
      });

      it("should handle errors properly", async () => {
        const mockError = new Error("GraphQL Error");
        const spy = jest.spyOn(authService, 'graphqlMutation').mockRejectedValue(mockError);
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        await expect(authService.createOrg("Test Org")).rejects.toThrow("GraphQL Error");
        expect(consoleSpy).toHaveBeenCalledWith("Error in createOrg: Error: GraphQL Error");

        spy.mockRestore();
        consoleSpy.mockRestore();
      });
    });

    describe("addRoleUser", () => {
      it("should call graphqlMutation with correct parameters including orgId", async () => {
        const mockResponse = { addRoleUser: "role-added-successfully" };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.addRoleUser("admin", "party-123", "SYSTEM_ADMIN");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // ADD_ROLE_USER_MUTATION
          {
            role: "admin",
            roleType: "SYSTEM_ADMIN",
            partyId: "party-123",
            orgId: authService.orgId
          }
        );
        expect(result).toBe("role-added-successfully");

        spy.mockRestore();
      });

      it("should work without roleType parameter", async () => {
        const mockResponse = { addRoleUser: "role-added-successfully" };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        await authService.addRoleUser("user", "party-456");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object),
          {
            role: "user",
            roleType: undefined,
            partyId: "party-456",
            orgId: authService.orgId
          }
        );

        spy.mockRestore();
      });
    });

    describe("createUserLogin", () => {
      it("should call graphqlMutation with correct userLoginId", async () => {
        const mockResponse = {
          createUserLogin: {
            id: "user-login-123",
            partyId: "party-456",
            type: "USER_LOGIN",
            username: "0971879660",
            status: "ACTIVE",
            accessToken: "mock-access-token-123"
          }
        };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.createUserLogin("user-login-123");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // CREATE_USER_LOGIN_MUTATION
          { userLoginId: "user-login-123" }
        );
        expect(result.id).toBe("user-login-123");
        expect(result.username).toBe("0971879660");
        expect(result.status).toBe("ACTIVE");
        expect(result.accessToken).toBe("mock-access-token-123");

        spy.mockRestore();
      });
    });

    describe("checkUserLogin", () => {
      it("should call graphqlQuery with correct userLoginId", async () => {
        const mockResponse = { checkUserLogin: true };
        const spy = jest.spyOn(authService, 'graphqlQuery').mockResolvedValue(mockResponse);

        const result = await authService.checkUserLogin("user-login-123");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // CHECK_USER_LOGIN query
          { userLoginId: "user-login-123" }
        );
        expect(result).toBe(true);

        spy.mockRestore();
      });

      it("should return false when user login does not exist", async () => {
        const mockResponse = { checkUserLogin: false };
        const spy = jest.spyOn(authService, 'graphqlQuery').mockResolvedValue(mockResponse);

        const result = await authService.checkUserLogin("non-existent-user");

        expect(result).toBe(false);

        spy.mockRestore();
      });
    });

    // Integration tests với real API (comment out để tránh gọi API thật khi test)
    // Uncomment khi muốn test với data thật

    // it("createOrg integration test", async () => {
    //   const response = await authService.createOrg(`Test-Org-${Date.now()}`);
    //   console.log("Create Org Response:", response);
    //   expect(response).toBeDefined();
    // }, 10000);

    // it("checkUserLogin integration test", async () => {
    //   const response = await authService.checkUserLogin("admin");
    //   console.log("Check User Login Response:", response);
    //   expect(response).toBeDefined();
    // }, 10000);
  });

  describe("OTP Functions", () => {
    describe("sendOTP", () => {
      it("should call graphqlMutation with correct parameters (default SMS)", async () => {
        const mockResponse = { sendOTP: { success: true, message: "OTP sent successfully" } };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.sendOTP("+84987654321");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // SEND_OTP_MUTATION
          {
            orgId: authService.orgId,
            phone: "+84987654321",
            channelType: "SMS"
          }
        );
        expect(result).toEqual({ success: true, message: "OTP sent successfully" });

        spy.mockRestore();
      });

      it("should call graphqlMutation with ZALO type", async () => {
        const mockResponse = { sendOTP: { success: true, message: "OTP sent via ZALO" } };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.sendOTP("+84987654321", "ZALO");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // SEND_OTP_MUTATION
          {
            orgId: authService.orgId,
            phone: "+84987654321",
            channelType: "ZALO"
          }
        );
        expect(result).toEqual({ success: true, message: "OTP sent via ZALO" });

        spy.mockRestore();
      });

      it("should handle errors properly", async () => {
        const mockError = new Error("Phone number invalid");
        const spy = jest.spyOn(authService, 'graphqlMutation').mockRejectedValue(mockError);
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        await expect(authService.sendOTP("invalid-phone")).rejects.toThrow("Phone number invalid");
        expect(consoleSpy).toHaveBeenCalledWith("Error in sendOTP: Error: Phone number invalid");

        spy.mockRestore();
        consoleSpy.mockRestore();
      });
    });

    describe("validateOTP", () => {
      it("should call graphqlMutation with correct parameters (default SMS)", async () => {
        const mockResponse = {
          validateOTP: "OTP validated successfully"
        };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.validateOTP("123456", "+84987654321");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // VALIDATE_OTP_MUTATION
          {
            otpCode: "123456",
            phone: "+84987654321",
            channelType: "SMS"
          }
        );
        expect(result.result).toBe("OTP validated successfully");

        spy.mockRestore();
      });

      it("should call graphqlMutation with ZALO type", async () => {
        const mockResponse = {
          validateOTP: "OTP validated via ZALO"
        };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.validateOTP("123456", "+84987654321", "ZALO");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // VALIDATE_OTP_MUTATION
          {
            otpCode: "123456",
            phone: "+84987654321",
            channelType: "ZALO"
          }
        );
        expect(result.result).toBe("OTP validated via ZALO");

        spy.mockRestore();
      });

      it("should handle invalid OTP", async () => {
        const mockResponse = {
          validateOTP: "Invalid OTP code"
        };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.validateOTP("000000", "+84987654321");

        expect(result.result).toBe("Invalid OTP code");

        spy.mockRestore();
      });

      it("should handle network errors", async () => {
        const mockError = new Error("Network error");
        const spy = jest.spyOn(authService, 'graphqlMutation').mockRejectedValue(mockError);
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        await expect(authService.validateOTP("123456", "+84987654321")).rejects.toThrow("Network error");
        expect(consoleSpy).toHaveBeenCalledWith("Error in validateOTP: Error: Network error");

        spy.mockRestore();
        consoleSpy.mockRestore();
      });
    });

    // Integration tests với real API (comment out để tránh gọi API thật khi test)
    // Uncomment khi muốn test với data thật

    // it("sendOTP integration test", async () => {
    //   const response = await authService.sendOTP("+84987654321");
    //   console.log("Send OTP Response:", response);
    //   expect(response.success).toBeDefined();
    // }, 10000);

    // it("validateOTP integration test", async () => {
    //   // First send OTP
    //   await authService.sendOTP("+84987654321");
    //
    //   // Then validate with actual OTP code
    //   const response = await authService.validateOTP("123456", "+84987654321");
    //   console.log("Validate OTP Response:", response);
    //   expect(response.success).toBeDefined();
    // }, 15000);

    describe("getAccessTokenByOTP", () => {
      it("should call graphqlMutation with correct parameters (default SMS)", async () => {
        const mockResponse = {
          getAccessTokenByOTP: "c6d78cb8-df77-47f5-a72a-dc301012307f"
        };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.getAccessTokenByOTP("123456", "+84987654321");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // GET_ACCESS_TOKEN_BY_OTP_MUTATION
          {
            otpCode: "123456",
            phone: "+84987654321",
            channelType: "SMS"
          }
        );
        expect(result.accessToken).toBe("c6d78cb8-df77-47f5-a72a-dc301012307f");

        spy.mockRestore();
      });

      it("should call graphqlMutation with ZALO type", async () => {
        const mockResponse = {
          getAccessTokenByOTP: "mock-access-token-zalo-456"
        };
        const spy = jest.spyOn(authService, 'graphqlMutation').mockResolvedValue(mockResponse);

        const result = await authService.getAccessTokenByOTP("654321", "+84987654321", "ZALO");

        expect(spy).toHaveBeenCalledWith(
          expect.any(Object), // GET_ACCESS_TOKEN_BY_OTP_MUTATION
          {
            otpCode: "654321",
            phone: "+84987654321",
            channelType: "ZALO"
          }
        );
        expect(result.accessToken).toBe("mock-access-token-zalo-456");

        spy.mockRestore();
      });

      it("should handle invalid OTP code", async () => {
        const mockError = new Error("Invalid OTP code");
        const spy = jest.spyOn(authService, 'graphqlMutation').mockRejectedValue(mockError);
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        await expect(authService.getAccessTokenByOTP("000000", "+84987654321")).rejects.toThrow("Invalid OTP code");
        expect(consoleSpy).toHaveBeenCalledWith("Error in getAccessTokenByOTP: Error: Invalid OTP code");

        spy.mockRestore();
        consoleSpy.mockRestore();
      });

      it("should handle expired OTP", async () => {
        const mockError = new Error("OTP has expired");
        const spy = jest.spyOn(authService, 'graphqlMutation').mockRejectedValue(mockError);
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        await expect(authService.getAccessTokenByOTP("123456", "+84987654321")).rejects.toThrow("OTP has expired");
        expect(consoleSpy).toHaveBeenCalledWith("Error in getAccessTokenByOTP: Error: OTP has expired");

        spy.mockRestore();
        consoleSpy.mockRestore();
      });

      it("should handle network errors", async () => {
        const mockError = new Error("Network connection failed");
        const spy = jest.spyOn(authService, 'graphqlMutation').mockRejectedValue(mockError);
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        await expect(authService.getAccessTokenByOTP("123456", "+84987654321")).rejects.toThrow("Network connection failed");
        expect(consoleSpy).toHaveBeenCalledWith("Error in getAccessTokenByOTP: Error: Network connection failed");

        spy.mockRestore();
        consoleSpy.mockRestore();
      });
    });

    // Integration test với real API (comment out để tránh gọi API thật khi test)
    // Uncomment khi muốn test với data thật

    // it("getAccessTokenByOTP integration test", async () => {
    //   // First send OTP
    //   await authService.sendOTP("+84987654321");
    //
    //   // Then get access token with actual OTP code
    //   const response = await authService.getAccessTokenByOTP("123456", "+84987654321");
    //   console.log("Get Access Token by OTP Response:", response);
    //   expect(response.accessToken).toBeDefined();
    //   expect(response.phone).toBe("+84987654321");
    // }, 20000);
  });

  // Add more integration tests for other methods...
});
