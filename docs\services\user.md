# User Service

The User Service handles customer and user management operations including customer profiles, company management, employee management, and location services.

## Table of Contents

- [Overview](#overview)
- [Customer Management](#customer-management)
- [Company Management](#company-management)
- [Employee Management](#employee-management)
- [Location Services](#location-services)
- [Search Operations](#search-operations)
- [Best Practices](#best-practices)

## Overview

The User Service provides comprehensive user and customer management functionality:

- **Customer Management**: Create, update, search, and manage customer profiles
- **Company Management**: Handle business customer accounts and company information
- **Employee Management**: Manage staff accounts and organizational structure
- **Location Services**: Handle provinces, districts, and wards for address management
- **Search Operations**: Advanced search capabilities across all user types

## Customer Management

### Search Customers

Search for customers with various filters:

```typescript
async function searchCustomers() {
  try {
    const customers = await sdk.user.searchCustomer({
      keyword: 'john',
      type: 'CUSTOMER',
      startCreatedDate: '2024-01-01',
      endCreatedDate: '2024-12-31',
      memberLevel: 'GOLD',
      currentPage: 1,
      pageSize: 20
    });
    
    console.log(`Found ${customers.length} customers`);
    return customers;
  } catch (error) {
    console.error('Customer search failed:', error);
    throw error;
  }
}
```

### Customer Search Parameters

```typescript
interface CustomerQuery {
  keyword?: string;           // Search in name, phone, email
  type?: string;              // Customer type filter
  startCreatedDate?: string;  // Filter by creation date range
  endCreatedDate?: string;    // Filter by creation date range
  memberLevel?: string;       // Member level filter
  currentPage?: number;       // Page number (1-based)
  pageSize?: number;          // Results per page
}
```

### Create Customer

Create a new customer profile:

```typescript
async function createCustomer() {
  const customerData = {
    name: 'Jane Doe',
    phone: '**********',
    email: '<EMAIL>',
    birthDate: '1990-01-01'
  };

  try {
    const customer = await sdk.user.createCustomerV2(
      customerData,
      'created-by-user-id'
    );
    
    console.log('Customer created:', {
      id: customer.id,
      name: customer.name,
      email: customer.email
    });
    
    return customer;
  } catch (error) {
    console.error('Customer creation failed:', error);
    throw error;
  }
}
```

### Customer Data Structure

```typescript
interface CreateCustomerRequest {
  name: string;
  phone?: string;
  email?: string;
  birthDate?: string;
  address?: string;
  gender?: string;
  identityNumber?: string;
}

interface Customer {
  id: string;
  name: string;
  address: string;
  gender: string;
  identityNumber: string;
  birthDate: Date;
  email: string;
  phone: string;
  createdStamp: Date;
  createdBy: string;
  memberLevel: string;
}
```

### Get Customer by ID

Retrieve a specific customer:

```typescript
async function getCustomerById(customerId: string) {
  try {
    const customer = await sdk.user.getCustomerById(customerId);
    
    console.log('Customer details:', {
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      memberLevel: customer.memberLevel
    });
    
    return customer;
  } catch (error) {
    console.error('Failed to fetch customer:', error);
    throw error;
  }
}
```

### Update Customer

Update customer information:

```typescript
async function updateCustomer(customerId: string) {
  const updateData = {
    name: 'Jane Smith',
    phone: '**********',
    email: '<EMAIL>',
    address: '456 New Street',
    birthDate: '1990-01-01'
  };

  try {
    const updatedCustomer = await sdk.user.updateCustomerV2(
      customerId,
      updateData,
      'updated-by-user-id'
    );
    
    console.log('Customer updated successfully');
    return updatedCustomer;
  } catch (error) {
    console.error('Customer update failed:', error);
    throw error;
  }
}
```

## Company Management

### Create Company

Create a new company account:

```typescript
async function createCompany() {
  const companyData = {
    name: 'Tech Solutions Ltd',
    taxCode: '**********',
    address: '123 Business District',
    phone: '**********',
    email: '<EMAIL>',
    contactPerson: 'John Manager',
    industry: 'Technology'
  };

  try {
    const company = await sdk.user.createCompany(
      companyData,
      'created-by-user-id'
    );
    
    console.log('Company created:', {
      id: company.id,
      name: company.name,
      taxCode: company.taxCode
    });
    
    return company;
  } catch (error) {
    console.error('Company creation failed:', error);
    throw error;
  }
}
```

### Search Companies

Search for companies:

```typescript
async function searchCompanies(keyword: string) {
  try {
    const companies = await sdk.user.searchCompany(
      keyword,
      1,    // currentPage
      20    // pageSize
    );
    
    console.log(`Found ${companies.length} companies`);
    return companies;
  } catch (error) {
    console.error('Company search failed:', error);
    throw error;
  }
}
```

### Company Data Structure

```typescript
interface CreateCompanyRequest {
  name: string;
  taxCode: string;
  address: string;
  phone: string;
  email: string;
  contactPerson: string;
  industry?: string;
  website?: string;
  description?: string;
}

interface Company {
  id: string;
  name: string;
  taxCode: string;
  address: string;
  phone: string;
  email: string;
  contactPerson: string;
  industry: string;
  createdStamp: Date;
  createdBy: string;
}
```

## Employee Management

### Search Employees

Search for employees with filters:

```typescript
async function searchEmployees() {
  const query = {
    keyword: 'john',
    department: 'IT',
    position: 'Developer',
    status: 'ACTIVE',
    currentPage: 1,
    pageSize: 20
  };

  try {
    const employees = await sdk.user.searchEmployees(query);
    
    console.log(`Found ${employees.length} employees`);
    return employees;
  } catch (error) {
    console.error('Employee search failed:', error);
    throw error;
  }
}
```

### Get Employee Positions

Retrieve positions for a specific employee:

```typescript
async function getEmployeePositions(employeeId: string) {
  try {
    const positions = await sdk.user.getPositionsByEmployeesId(employeeId);
    
    console.log('Employee positions:', positions);
    return positions;
  } catch (error) {
    console.error('Failed to fetch employee positions:', error);
    throw error;
  }
}
```

### Get Employee Store Channels

Get store channels assigned to an employee:

```typescript
async function getEmployeeStoreChannels(employeeId: string) {
  try {
    const storeChannels = await sdk.user.getStoreChannelIdsByEmployeesId(employeeId);
    
    console.log('Employee store channels:', storeChannels);
    return storeChannels;
  } catch (error) {
    console.error('Failed to fetch employee store channels:', error);
    throw error;
  }
}
```

### Employee Query Interface

```typescript
interface EmployeesQuery {
  keyword?: string;
  department?: string;
  position?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  currentPage?: number;
  pageSize?: number;
}
```

## Location Services

### Get Provinces

Retrieve all provinces:

```typescript
async function getProvinces() {
  try {
    const provinces = await sdk.user.getProvinces();
    
    console.log('Available provinces:', provinces.map(p => ({
      code: p.code,
      name: p.name
    })));
    
    return provinces;
  } catch (error) {
    console.error('Failed to fetch provinces:', error);
    throw error;
  }
}
```

### Get Districts

Get districts for a specific province:

```typescript
async function getDistricts(provinceCode: string) {
  try {
    const districts = await sdk.user.getDistricts(provinceCode);
    
    console.log(`Districts in ${provinceCode}:`, districts.map(d => ({
      code: d.code,
      name: d.name
    })));
    
    return districts;
  } catch (error) {
    console.error('Failed to fetch districts:', error);
    throw error;
  }
}
```

### Get Wards

Get wards for a specific district:

```typescript
async function getWards(districtCode: string) {
  try {
    const wards = await sdk.user.getWards(districtCode);
    
    console.log(`Wards in ${districtCode}:`, wards.map(w => ({
      code: w.code,
      name: w.name
    })));
    
    return wards;
  } catch (error) {
    console.error('Failed to fetch wards:', error);
    throw error;
  }
}
```

### Location Helper Class

```typescript
class LocationHelper {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getFullAddress(provinceCode: string, districtCode: string, wardCode: string) {
    try {
      const [provinces, districts, wards] = await Promise.all([
        this.sdk.user.getProvinces(),
        this.sdk.user.getDistricts(provinceCode),
        this.sdk.user.getWards(districtCode)
      ]);

      const province = provinces.find(p => p.code === provinceCode);
      const district = districts.find(d => d.code === districtCode);
      const ward = wards.find(w => w.code === wardCode);

      return {
        province: province?.name || '',
        district: district?.name || '',
        ward: ward?.name || '',
        fullAddress: `${ward?.name || ''}, ${district?.name || ''}, ${province?.name || ''}`
      };
    } catch (error) {
      console.error('Failed to get full address:', error);
      throw error;
    }
  }

  async validateAddress(provinceCode: string, districtCode: string, wardCode: string): Promise<boolean> {
    try {
      const address = await this.getFullAddress(provinceCode, districtCode, wardCode);
      return !!(address.province && address.district && address.ward);
    } catch (error) {
      return false;
    }
  }
}
```

## Search Operations

### Advanced Customer Search

```typescript
class CustomerSearchManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async searchByKeyword(keyword: string, options: {
    type?: string;
    memberLevel?: string;
    pageSize?: number;
  } = {}) {
    return await this.sdk.user.searchCustomer({
      keyword,
      type: options.type || 'CUSTOMER',
      memberLevel: options.memberLevel,
      currentPage: 1,
      pageSize: options.pageSize || 20
    });
  }

  async searchByDateRange(startDate: string, endDate: string) {
    return await this.sdk.user.searchCustomer({
      startCreatedDate: startDate,
      endCreatedDate: endDate,
      currentPage: 1,
      pageSize: 50
    });
  }

  async searchByMemberLevel(memberLevel: string) {
    return await this.sdk.user.searchCustomer({
      memberLevel,
      currentPage: 1,
      pageSize: 100
    });
  }

  async getAllCustomers(): Promise<any[]> {
    let allCustomers: any[] = [];
    let currentPage = 1;
    const pageSize = 50;

    while (true) {
      const customers = await this.sdk.user.searchCustomer({
        currentPage,
        pageSize
      });

      if (customers.length === 0) break;

      allCustomers.push(...customers);
      currentPage++;
    }

    return allCustomers;
  }
}
```

## Best Practices

### 1. Input Validation

```typescript
// ✅ Good: Validate customer data before creation
function validateCustomerData(data: CreateCustomerRequest): string[] {
  const errors: string[] = [];
  
  if (!data.name || data.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters');
  }
  
  if (data.email && !isValidEmail(data.email)) {
    errors.push('Invalid email format');
  }
  
  if (data.phone && !isValidPhone(data.phone)) {
    errors.push('Invalid phone number format');
  }
  
  if (data.birthDate && !isValidDate(data.birthDate)) {
    errors.push('Invalid birth date format');
  }
  
  return errors;
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[0-9]{10,11}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
}

function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}
```

### 2. Error Handling

```typescript
// ✅ Good: Handle specific user service errors
async function safeCustomerOperation(operation: () => Promise<any>) {
  try {
    return await operation();
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('Customer not found');
      return null;
    } else if (error.response?.status === 409) {
      console.error('Customer already exists');
      throw new Error('Customer with this email/phone already exists');
    } else if (error.response?.status === 422) {
      console.error('Invalid customer data');
      throw new Error('Please check customer information and try again');
    }
    throw error;
  }
}
```

### 3. Caching Location Data

```typescript
// ✅ Good: Cache location data to reduce API calls
class LocationCache {
  private cache = new Map<string, any>();
  private cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours

  async getProvinces(sdk: SDK): Promise<any[]> {
    const cacheKey = 'provinces';
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const provinces = await sdk.user.getProvinces();
    this.cache.set(cacheKey, {
      data: provinces,
      timestamp: Date.now()
    });

    return provinces;
  }

  async getDistricts(sdk: SDK, provinceCode: string): Promise<any[]> {
    const cacheKey = `districts_${provinceCode}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const districts = await sdk.user.getDistricts(provinceCode);
    this.cache.set(cacheKey, {
      data: districts,
      timestamp: Date.now()
    });

    return districts;
  }
}
```

### 4. Batch Operations

```typescript
// ✅ Good: Process multiple customers efficiently
class CustomerBatchProcessor {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async createMultipleCustomers(customersData: CreateCustomerRequest[], createdBy: string) {
    const results = await Promise.allSettled(
      customersData.map(customerData => 
        this.sdk.user.createCustomerV2(customerData, createdBy)
      )
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log(`Batch customer creation: ${successful} successful, ${failed} failed`);
    return results;
  }

  async getMultipleCustomers(customerIds: string[]) {
    const results = await Promise.allSettled(
      customerIds.map(id => this.sdk.user.getCustomerById(id))
    );

    return results
      .filter(result => result.status === 'fulfilled')
      .map(result => (result as PromiseFulfilledResult<any>).value);
  }
}
```

---

**Next Steps:**
- [Payment Service](./payment.md) - Payment processing and invoicing
- [CRM Service](./crm.md) - Customer relationship management
- [Warehouse Service](./warehouse.md) - Inventory management
