export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  partyId: string;
  orgId: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  identityNumber: string;
  gender: string;
  birthDate: string;
  avatarUrl: string;
  accessToken: string;
  username: string;
  orgPermissionsMap: Record<string, any>; // Assuming it's a map of permissions
  orgPositionsMap: Record<string, any>; // Assuming it's a map of positions
  orgRolesMap: Record<string, any>; // Assuming it's a map of roles
}

export interface RegisterRequest {
  username: string;
  fullName: string;
  password: string;
  userIP: string;
}

export interface RegisterResponse {
  id: string;
  partyId: string;
  type: string;
  username: string;
  status: string;
  accessToken: string;
}

export interface SendSmsVerifyCodeResponse {
  id: string;
  code: string;
  username: string;
  timeExpired: string; // This should ideally be a Date object, but it depends on the format returned by your GraphQL server
}

export interface SmsVerifyCodeRequest {
  username: string;
  code: string;
}

export interface ResetPasswordRequest {
  username: string;
  newPassword: string;
  accessToken: string;
}

export interface ResetPasswordResponse {
  success: boolean;
}



export interface UpdateInfoRequest {
  orgId?: string | null;
  accessToken?: string | null;
  updateUserRequest: {
      fullName?: string | null;
      address?: string | null;
      gender?: string | null;
      birthDateLongTime?: string | null;
      birthDate?: string | null;
      email?: string | null;
      identityNumber?: string | null;
      phone?: string | null;
      imageUrl?: string | null;
      personalTitle?: string | null;
  };
  type?: string | null;
  password?: string | null;
}

export interface UpdateInfoResponse {
  partyId: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  identityNumber: string;
  gender: string;
  birthDate: string;
  avatarUrl: string;
}

/**
 * Request interface for sending OTP
 */
export interface SendOTPRequest {
  /** Organization ID */
  orgId: string;

  /** Phone number to send OTP to */
  phone: string;

  /** Type of OTP delivery - SMS or ZALO (defaults to SMS) */
  type?: 'SMS' | 'ZALO';
}

/**
 * Response interface for sending OTP
 */
export interface SendOTPResponse {
  /** SMS ID */
  id?: string;
  /** OTP code (for development) */
  code?: string;
  /** Username/phone number */
  username?: string;
  /** Expiration timestamp */
  timeExpired?: number;
}

/**
 * Request interface for validating OTP
 */
export interface ValidateOTPRequest {
  /** OTP code to validate */
  otpCode: string;

  /** Phone number associated with the OTP */
  phone: string;
}

/**
 * Response interface for validating OTP
 * validateOTP returns a simple string result
 */
export interface ValidateOTPResponse {
  /** Result message from validateOTP */
  result: string;
}

/**
 * Response interface for creating user login
 */
export interface CreateUserLoginResponse {
  /** User login ID */
  id: string;

  /** Party ID associated with the user login */
  partyId: string;

  /** Type of user login */
  type: string;

  /** Username */
  username: string;

  /** Status of the user login */
  status: string;

  /** Access token for the user */
  accessToken: string;
}

/**
 * Request interface for getting access token by OTP
 */
export interface GetAccessTokenByOTPRequest {
  /** OTP code to validate */
  otpCode: string;

  /** Phone number associated with the OTP */
  phone: string;

  /** Type of OTP delivery - SMS or ZALO (defaults to SMS) */
  type?: 'SMS' | 'ZALO';
}

/**
 * Response interface for getting access token by OTP
 * Returns just the access token as a string
 */
export interface GetAccessTokenByOTPResponse {
  /** The access token string */
  accessToken: string;
}
