export function validatePhoneNumber(phoneNumber: string): boolean {
    const urlParams = new URLSearchParams(window.location.search);
    const country: string = urlParams.get("country")?.trim() || "default";
  
    const pattern = (() => {
      switch (country) {
        case "vietnam":
          return /^(0?)(3[2-9]|5[6|8|9]|7[0|6-9]|8[0-6|8|9]|9[0-4|6-9])[0-9]{7}$/;
        case "quocte":
          return /^(\+|00)[1-9][0-9]{1,3}[0-9]{4,14}$/;
        default:
          return /^(0?)(3[2-9]|5[6|8|9]|7[0|6-9]|8[0-6|8|9]|9[0-4|6-9])[0-9]{7}$|^(\+|00)[1-9][0-9]{1,3}[0-9]{4,14}$/;
      }
    })();
  
    return pattern.test(phoneNumber);
  }



  