# Product Service

The Product Service handles all product-related operations including catalog browsing, product management, categories, and inventory information.

## Table of Contents

- [Overview](#overview)
- [Product Retrieval](#product-retrieval)
- [Product Search & Filtering](#product-search--filtering)
- [Category Management](#category-management)
- [Product Management](#product-management)
- [Cache Management](#cache-management)
- [Best Practices](#best-practices)

## Overview

The Product Service provides comprehensive product catalog functionality:

- **Product Retrieval**: Get products by ID, slug, or search criteria
- **Advanced Filtering**: Filter by price, category, brand, and more
- **Category Management**: Browse and manage product categories
- **Product Updates**: Update product information (admin functions)
- **Cache Management**: Optimize performance with caching

## Product Retrieval

### Get Product by ID

Retrieve a specific product using its unique identifier:

```typescript
async function getProductById(productId: string) {
  try {
    const product = await sdk.product.getProductById(productId);
    
    console.log('Product details:', {
      id: product.id,
      title: product.title,
      price: product.price,
      available: product.available,
      sku: product.sku
    });
    
    return product;
  } catch (error) {
    console.error('Failed to fetch product:', error);
    throw error;
  }
}
```

### Get Product by Slug

Retrieve a product using its SEO-friendly slug:

```typescript
async function getProductBySlug(slug: string) {
  try {
    const product = await sdk.product.getProductBySlug(slug);
    
    console.log('Product found:', product.title);
    return product;
  } catch (error) {
    console.error('Product not found:', error);
    throw error;
  }
}
```

### Product Data Structure

```typescript
interface Product {
  id: string;                    // Unique product identifier
  title: string;                 // Product name
  description: string;           // Product description
  sku: string;                   // Stock keeping unit
  price: number;                 // Product price
  available: boolean;            // Availability status
  categories: Category[];        // Product categories
  featuredImage: string;         // Main product image URL
  subType: string;              // Product sub-type
  brandId?: string;             // Brand identifier
  tags?: string[];              // Product tags
  variants?: ProductVariant[];   // Product variants
  attributes?: ProductAttribute[]; // Product attributes
}
```

## Product Search & Filtering

### Basic Product Search

Search products with pagination:

```typescript
async function searchProducts(options: {
  keyword?: string;
  currentPage?: number;
  maxResult?: number;
}) {
  try {
    const products = await sdk.product.getSimpleProducts({
      keyword: options.keyword,
      currentPage: options.currentPage || 1,
      maxResult: options.maxResult || 20
    });
    
    console.log(`Found ${products.length} products`);
    return products;
  } catch (error) {
    console.error('Product search failed:', error);
    throw error;
  }
}
```

### Advanced Filtering

Use comprehensive filters for product search:

```typescript
async function advancedProductSearch() {
  try {
    const products = await sdk.product.getSimpleProducts({
      partnerId: 'your-org-id',
      storeChannel: 'your-store-id',
      keyword: 'laptop',
      category: 'electronics',
      priceFrom: 500000,        // Minimum price
      priceTo: 2000000,         // Maximum price
      brandId: 'brand-123',
      productType: 'PHYSICAL',
      subType: 'LAPTOP',
      status: 'ACTIVE',
      display: true,
      onlyPromotion: false,
      currentPage: 1,
      maxResult: 50
    });
    
    return products;
  } catch (error) {
    console.error('Advanced search failed:', error);
    throw error;
  }
}
```

### Filter Options Interface

```typescript
interface ProductFilterOptions {
  partnerId?: string;           // Organization ID
  storeChannel?: string;        // Store channel ID
  category?: string;            // Category ID
  product?: string;             // Specific product ID
  sku?: string;                 // Product SKU
  tag?: string;                 // Product tag
  priceFrom?: number;           // Minimum price
  priceTo?: number;             // Maximum price
  status?: string;              // Product status
  productType?: string;         // Product type
  subType?: string;             // Product sub-type
  brandId?: string;             // Brand ID
  keyword?: string;             // Search keyword
  display?: boolean;            // Display status
  onlyPromotion?: boolean;      // Only promotional products
  currentPage?: number;         // Page number (1-based)
  maxResult?: number;           // Results per page
}
```

### Pagination Helper

```typescript
class ProductPaginator {
  private sdk: SDK;
  private filters: ProductFilterOptions;
  private currentPage = 1;
  private pageSize = 20;

  constructor(sdk: SDK, filters: ProductFilterOptions = {}) {
    this.sdk = sdk;
    this.filters = filters;
  }

  async getPage(page: number): Promise<Product[]> {
    this.currentPage = page;
    return await this.sdk.product.getSimpleProducts({
      ...this.filters,
      currentPage: page,
      maxResult: this.pageSize
    });
  }

  async getNextPage(): Promise<Product[]> {
    return await this.getPage(this.currentPage + 1);
  }

  async getPreviousPage(): Promise<Product[]> {
    if (this.currentPage > 1) {
      return await this.getPage(this.currentPage - 1);
    }
    return [];
  }

  async getAllProducts(): Promise<Product[]> {
    let allProducts: Product[] = [];
    let page = 1;
    
    while (true) {
      const products = await this.getPage(page);
      if (products.length === 0) break;
      
      allProducts.push(...products);
      page++;
    }
    
    return allProducts;
  }
}

// Usage
const paginator = new ProductPaginator(sdk, {
  keyword: 'laptop',
  priceFrom: 500000,
  priceTo: 2000000
});

const firstPage = await paginator.getPage(1);
const allProducts = await paginator.getAllProducts();
```

## Category Management

### Get Categories

Retrieve product categories with hierarchy:

```typescript
async function getCategories(typeBuild: string = 'web', level: number = 1) {
  try {
    const categories = await sdk.product.getCategory(typeBuild, level);
    
    console.log('Categories:', categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      parentId: cat.parentId,
      level: cat.level
    })));
    
    return categories;
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    throw error;
  }
}
```

### Category Tree Builder

```typescript
interface Category {
  id: string;
  name: string;
  parentId?: string;
  level: number;
  children?: Category[];
}

class CategoryTreeBuilder {
  static buildTree(categories: Category[]): Category[] {
    const categoryMap = new Map<string, Category>();
    const rootCategories: Category[] = [];

    // Initialize all categories with empty children array
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Build the tree structure
    categories.forEach(category => {
      const categoryNode = categoryMap.get(category.id)!;
      
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children!.push(categoryNode);
        }
      } else {
        rootCategories.push(categoryNode);
      }
    });

    return rootCategories;
  }

  static findCategoryPath(categories: Category[], targetId: string): Category[] {
    const path: Category[] = [];
    
    function findPath(cats: Category[], target: string): boolean {
      for (const category of cats) {
        path.push(category);
        
        if (category.id === target) {
          return true;
        }
        
        if (category.children && findPath(category.children, target)) {
          return true;
        }
        
        path.pop();
      }
      return false;
    }
    
    const tree = this.buildTree(categories);
    findPath(tree, targetId);
    return path;
  }
}

// Usage
const categories = await getCategories();
const categoryTree = CategoryTreeBuilder.buildTree(categories);
const pathToCategory = CategoryTreeBuilder.findCategoryPath(categories, 'category-id');
```

## Product Management

### Update Product Information

These methods are typically used by administrators to manage product data:

#### Update Product Title

```typescript
async function updateProductTitle(productId: string, newTitle: string) {
  try {
    await sdk.product.updateProductTitle(productId, newTitle);
    console.log('Product title updated successfully');
  } catch (error) {
    console.error('Failed to update product title:', error);
    throw error;
  }
}
```

#### Update Product Price

```typescript
async function updateProductPrice(productId: string, newPrice: number) {
  try {
    await sdk.product.updatePrice(productId, newPrice);
    console.log('Product price updated successfully');
  } catch (error) {
    console.error('Failed to update product price:', error);
    throw error;
  }
}
```

#### Update Promotion Price

```typescript
async function updatePromotionPrice(productId: string, promotionPrice: number) {
  try {
    await sdk.product.updatePricePromotion(productId, promotionPrice);
    console.log('Promotion price updated successfully');
  } catch (error) {
    console.error('Failed to update promotion price:', error);
    throw error;
  }
}
```

#### Update Product Category

```typescript
async function updateProductCategory(productId: string, categoryId: string) {
  try {
    await sdk.product.updateCategory(productId, categoryId);
    console.log('Product category updated successfully');
  } catch (error) {
    console.error('Failed to update product category:', error);
    throw error;
  }
}
```

#### Update Product Description

```typescript
async function updateProductDescription(productId: string, description: string) {
  try {
    await sdk.product.updateShortDescription(productId, description);
    console.log('Product description updated successfully');
  } catch (error) {
    console.error('Failed to update product description:', error);
    throw error;
  }
}
```

#### Update Product Unit

```typescript
async function updateProductUnit(productId: string, unit: string) {
  try {
    await sdk.product.updateUnit(productId, unit);
    console.log('Product unit updated successfully');
  } catch (error) {
    console.error('Failed to update product unit:', error);
    throw error;
  }
}
```

### Batch Product Updates

```typescript
class ProductBatchUpdater {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async updateMultipleProducts(updates: Array<{
    productId: string;
    field: 'title' | 'price' | 'category' | 'description' | 'unit';
    value: string | number;
  }>) {
    const results = await Promise.allSettled(
      updates.map(async (update) => {
        switch (update.field) {
          case 'title':
            return await this.sdk.product.updateProductTitle(
              update.productId, 
              update.value as string
            );
          case 'price':
            return await this.sdk.product.updatePrice(
              update.productId, 
              update.value as number
            );
          case 'category':
            return await this.sdk.product.updateCategory(
              update.productId, 
              update.value as string
            );
          case 'description':
            return await this.sdk.product.updateShortDescription(
              update.productId, 
              update.value as string
            );
          case 'unit':
            return await this.sdk.product.updateUnit(
              update.productId, 
              update.value as string
            );
          default:
            throw new Error(`Unknown field: ${update.field}`);
        }
      })
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log(`Batch update completed: ${successful} successful, ${failed} failed`);
    return results;
  }
}
```

## Cache Management

### Clear Product Caches

Clear all product-related caches to ensure fresh data:

```typescript
async function clearProductCaches() {
  try {
    await sdk.product.clearAllCaches();
    console.log('Product caches cleared successfully');
  } catch (error) {
    console.error('Failed to clear caches:', error);
    throw error;
  }
}
```

### Cache Strategy

```typescript
class ProductCacheManager {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  async getProductWithCache(productId: string): Promise<Product> {
    const cacheKey = `product_${productId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      console.log('Returning cached product');
      return cached.data;
    }

    console.log('Fetching fresh product data');
    const product = await sdk.product.getProductById(productId);
    
    this.cache.set(cacheKey, {
      data: product,
      timestamp: Date.now()
    });

    return product;
  }

  clearCache() {
    this.cache.clear();
  }

  clearProductCache(productId: string) {
    this.cache.delete(`product_${productId}`);
  }
}
```

## Best Practices

### 1. Efficient Product Loading

```typescript
// ✅ Good: Load products with appropriate page sizes
async function loadProductsEfficiently() {
  const products = await sdk.product.getSimpleProducts({
    currentPage: 1,
    maxResult: 20, // Reasonable page size
    display: true  // Only show displayable products
  });
  
  return products;
}

// ❌ Avoid: Loading too many products at once
async function inefficientLoading() {
  const products = await sdk.product.getSimpleProducts({
    maxResult: 1000 // Too many products
  });
}
```

### 2. Error Handling

```typescript
// ✅ Good: Handle specific product errors
async function safeProductRetrieval(productId: string) {
  try {
    return await sdk.product.getProductById(productId);
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('Product not found');
      return null;
    }
    throw error; // Re-throw other errors
  }
}
```

### 3. Search Optimization

```typescript
// ✅ Good: Debounced search
class ProductSearchManager {
  private searchTimeout: NodeJS.Timeout | null = null;

  debouncedSearch(keyword: string, callback: (products: Product[]) => void) {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    this.searchTimeout = setTimeout(async () => {
      try {
        const products = await sdk.product.getSimpleProducts({
          keyword,
          currentPage: 1,
          maxResult: 10
        });
        callback(products);
      } catch (error) {
        console.error('Search failed:', error);
      }
    }, 300); // 300ms debounce
  }
}
```

---

**Next Steps:**
- [Order Service](./order.md) - Order processing and management
- [User Service](./user.md) - Customer management
- [Payment Service](./payment.md) - Payment processing
