# Campaign Service

The Campaign Service manages marketing campaigns, promotional activities, discount codes, and customer engagement initiatives.

## Table of Contents

- [Overview](#overview)
- [Campaign Management](#campaign-management)
- [Discount & Promotion Handling](#discount--promotion-handling)
- [Campaign Analytics](#campaign-analytics)
- [Best Practices](#best-practices)

## Overview

The Campaign Service provides comprehensive marketing campaign functionality:

- **Campaign Creation**: Create and manage marketing campaigns
- **Promotion Management**: Handle discounts, coupons, and special offers
- **Target Audience**: Define and manage campaign target audiences
- **Performance Tracking**: Monitor campaign effectiveness and ROI
- **Automated Campaigns**: Set up triggered and scheduled campaigns

## Campaign Management

### Campaign Data Structure

```typescript
interface Campaign {
  id: string;
  name: string;
  description: string;
  type: CampaignType;
  status: CampaignStatus;
  startDate: Date;
  endDate: Date;
  budget: number;
  targetAudience: TargetAudience;
  channels: CampaignChannel[];
  metrics: CampaignMetrics;
  createdBy: string;
  createdDate: Date;
}

type CampaignType = 
  | 'PROMOTIONAL'
  | 'SEASONAL'
  | 'PRODUCT_LAUNCH'
  | 'RETENTION'
  | 'ACQUISITION'
  | 'LOYALTY';

type CampaignStatus = 
  | 'DRAFT'
  | 'SCHEDULED'
  | 'ACTIVE'
  | 'PAUSED'
  | 'COMPLETED'
  | 'CANCELLED';

interface TargetAudience {
  segments: string[];
  demographics: {
    ageRange?: [number, number];
    gender?: string;
    location?: string[];
  };
  behavioral: {
    purchaseHistory?: string;
    engagementLevel?: string;
  };
}

type CampaignChannel = 
  | 'EMAIL'
  | 'SMS'
  | 'PUSH_NOTIFICATION'
  | 'SOCIAL_MEDIA'
  | 'WEBSITE_BANNER'
  | 'IN_APP';
```

### Campaign Operations

```typescript
class CampaignManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async createCampaign(campaignData: CreateCampaignRequest) {
    try {
      // Validate campaign data
      const validationErrors = this.validateCampaignData(campaignData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Create campaign (placeholder - actual implementation would use SDK method)
      const campaign = await this.createCampaignAPI(campaignData);
      
      console.log('Campaign created:', {
        id: campaign.id,
        name: campaign.name,
        type: campaign.type,
        status: campaign.status
      });
      
      return campaign;
    } catch (error) {
      console.error('Campaign creation failed:', error);
      throw error;
    }
  }

  async getCampaignList(filters: CampaignFilters = {}) {
    try {
      // Placeholder for actual SDK method
      const campaigns = await this.getCampaignsAPI(filters);
      
      console.log(`Found ${campaigns.length} campaigns`);
      return campaigns;
    } catch (error) {
      console.error('Failed to fetch campaigns:', error);
      throw error;
    }
  }

  async updateCampaignStatus(campaignId: string, status: CampaignStatus) {
    try {
      await this.updateCampaignStatusAPI(campaignId, status);
      console.log(`Campaign ${campaignId} status updated to ${status}`);
    } catch (error) {
      console.error('Failed to update campaign status:', error);
      throw error;
    }
  }

  private validateCampaignData(data: CreateCampaignRequest): string[] {
    const errors: string[] = [];
    
    if (!data.name || data.name.trim().length < 3) {
      errors.push('Campaign name must be at least 3 characters');
    }
    
    if (!data.startDate || new Date(data.startDate) < new Date()) {
      errors.push('Start date must be in the future');
    }
    
    if (!data.endDate || new Date(data.endDate) <= new Date(data.startDate)) {
      errors.push('End date must be after start date');
    }
    
    if (!data.budget || data.budget <= 0) {
      errors.push('Budget must be greater than 0');
    }
    
    return errors;
  }

  // Placeholder methods - these would be actual SDK calls
  private async createCampaignAPI(data: CreateCampaignRequest): Promise<Campaign> {
    // Implementation would use actual SDK method
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async getCampaignsAPI(filters: CampaignFilters): Promise<Campaign[]> {
    // Implementation would use actual SDK method
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async updateCampaignStatusAPI(campaignId: string, status: CampaignStatus): Promise<void> {
    // Implementation would use actual SDK method
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }
}

interface CreateCampaignRequest {
  name: string;
  description: string;
  type: CampaignType;
  startDate: string;
  endDate: string;
  budget: number;
  targetAudience: TargetAudience;
  channels: CampaignChannel[];
}

interface CampaignFilters {
  status?: CampaignStatus;
  type?: CampaignType;
  startDate?: string;
  endDate?: string;
  createdBy?: string;
}
```

## Discount & Promotion Handling

### Discount Code Management

```typescript
interface DiscountCode {
  id: string;
  code: string;
  name: string;
  type: DiscountType;
  value: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  usageLimit?: number;
  usageCount: number;
  validFrom: Date;
  validTo: Date;
  isActive: boolean;
  applicableProducts?: string[];
  applicableCategories?: string[];
}

type DiscountType = 
  | 'PERCENTAGE'
  | 'FIXED_AMOUNT'
  | 'FREE_SHIPPING'
  | 'BUY_X_GET_Y';

class DiscountManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async createDiscountCode(discountData: CreateDiscountRequest) {
    try {
      const validationErrors = this.validateDiscountData(discountData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Generate unique code if not provided
      if (!discountData.code) {
        discountData.code = this.generateDiscountCode();
      }

      const discount = await this.createDiscountAPI(discountData);
      
      console.log('Discount code created:', {
        id: discount.id,
        code: discount.code,
        type: discount.type,
        value: discount.value
      });
      
      return discount;
    } catch (error) {
      console.error('Discount code creation failed:', error);
      throw error;
    }
  }

  async validateDiscountCode(code: string, orderAmount: number, productIds: string[] = []) {
    try {
      const discount = await this.getDiscountByCodeAPI(code);
      
      if (!discount) {
        return { isValid: false, reason: 'Discount code not found' };
      }

      if (!discount.isActive) {
        return { isValid: false, reason: 'Discount code is inactive' };
      }

      if (new Date() < discount.validFrom || new Date() > discount.validTo) {
        return { isValid: false, reason: 'Discount code has expired' };
      }

      if (discount.usageLimit && discount.usageCount >= discount.usageLimit) {
        return { isValid: false, reason: 'Discount code usage limit reached' };
      }

      if (discount.minOrderAmount && orderAmount < discount.minOrderAmount) {
        return { 
          isValid: false, 
          reason: `Minimum order amount is ${discount.minOrderAmount}` 
        };
      }

      // Check product/category restrictions
      if (discount.applicableProducts && discount.applicableProducts.length > 0) {
        const hasApplicableProduct = productIds.some(id => 
          discount.applicableProducts!.includes(id)
        );
        if (!hasApplicableProduct) {
          return { isValid: false, reason: 'Discount not applicable to selected products' };
        }
      }

      const discountAmount = this.calculateDiscountAmount(discount, orderAmount);
      
      return {
        isValid: true,
        discount,
        discountAmount,
        finalAmount: orderAmount - discountAmount
      };
    } catch (error) {
      console.error('Discount validation failed:', error);
      return { isValid: false, reason: 'Validation error occurred' };
    }
  }

  private calculateDiscountAmount(discount: DiscountCode, orderAmount: number): number {
    let discountAmount = 0;

    switch (discount.type) {
      case 'PERCENTAGE':
        discountAmount = (orderAmount * discount.value) / 100;
        break;
      case 'FIXED_AMOUNT':
        discountAmount = discount.value;
        break;
      case 'FREE_SHIPPING':
        discountAmount = 0; // Shipping cost would be handled separately
        break;
      default:
        discountAmount = 0;
    }

    // Apply maximum discount limit
    if (discount.maxDiscountAmount && discountAmount > discount.maxDiscountAmount) {
      discountAmount = discount.maxDiscountAmount;
    }

    return Math.min(discountAmount, orderAmount);
  }

  private generateDiscountCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private validateDiscountData(data: CreateDiscountRequest): string[] {
    const errors: string[] = [];
    
    if (!data.name || data.name.trim().length < 3) {
      errors.push('Discount name must be at least 3 characters');
    }
    
    if (!data.value || data.value <= 0) {
      errors.push('Discount value must be greater than 0');
    }
    
    if (data.type === 'PERCENTAGE' && data.value > 100) {
      errors.push('Percentage discount cannot exceed 100%');
    }
    
    if (!data.validFrom || !data.validTo) {
      errors.push('Valid from and valid to dates are required');
    }
    
    if (new Date(data.validTo) <= new Date(data.validFrom)) {
      errors.push('Valid to date must be after valid from date');
    }
    
    return errors;
  }

  // Placeholder methods
  private async createDiscountAPI(data: CreateDiscountRequest): Promise<DiscountCode> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async getDiscountByCodeAPI(code: string): Promise<DiscountCode | null> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }
}

interface CreateDiscountRequest {
  code?: string;
  name: string;
  type: DiscountType;
  value: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  usageLimit?: number;
  validFrom: string;
  validTo: string;
  applicableProducts?: string[];
  applicableCategories?: string[];
}
```

## Campaign Analytics

### Performance Tracking

```typescript
interface CampaignMetrics {
  impressions: number;
  clicks: number;
  conversions: number;
  revenue: number;
  cost: number;
  clickThroughRate: number;
  conversionRate: number;
  returnOnAdSpend: number;
  costPerAcquisition: number;
}

class CampaignAnalytics {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getCampaignPerformance(campaignId: string, dateRange: DateRange) {
    try {
      const metrics = await this.getCampaignMetricsAPI(campaignId, dateRange);
      
      const performance = {
        campaignId,
        dateRange,
        metrics,
        calculatedMetrics: this.calculateDerivedMetrics(metrics),
        insights: this.generateInsights(metrics)
      };

      return performance;
    } catch (error) {
      console.error('Failed to get campaign performance:', error);
      throw error;
    }
  }

  async compareCampaigns(campaignIds: string[], dateRange: DateRange) {
    try {
      const campaignPerformances = await Promise.all(
        campaignIds.map(id => this.getCampaignPerformance(id, dateRange))
      );

      const comparison = {
        campaigns: campaignPerformances,
        bestPerforming: this.findBestPerformingCampaign(campaignPerformances),
        recommendations: this.generateComparisonRecommendations(campaignPerformances)
      };

      return comparison;
    } catch (error) {
      console.error('Campaign comparison failed:', error);
      throw error;
    }
  }

  private calculateDerivedMetrics(metrics: CampaignMetrics) {
    return {
      clickThroughRate: metrics.impressions > 0 ? (metrics.clicks / metrics.impressions) * 100 : 0,
      conversionRate: metrics.clicks > 0 ? (metrics.conversions / metrics.clicks) * 100 : 0,
      returnOnAdSpend: metrics.cost > 0 ? (metrics.revenue / metrics.cost) * 100 : 0,
      costPerAcquisition: metrics.conversions > 0 ? metrics.cost / metrics.conversions : 0,
      revenuePerClick: metrics.clicks > 0 ? metrics.revenue / metrics.clicks : 0
    };
  }

  private generateInsights(metrics: CampaignMetrics): string[] {
    const insights: string[] = [];
    const derived = this.calculateDerivedMetrics(metrics);

    if (derived.clickThroughRate > 5) {
      insights.push('Excellent click-through rate - campaign is highly engaging');
    } else if (derived.clickThroughRate < 1) {
      insights.push('Low click-through rate - consider improving ad creative or targeting');
    }

    if (derived.conversionRate > 10) {
      insights.push('High conversion rate - campaign is effectively driving sales');
    } else if (derived.conversionRate < 2) {
      insights.push('Low conversion rate - review landing page and offer');
    }

    if (derived.returnOnAdSpend > 400) {
      insights.push('Excellent ROI - consider increasing budget');
    } else if (derived.returnOnAdSpend < 200) {
      insights.push('Low ROI - optimize targeting or reduce costs');
    }

    return insights;
  }

  private findBestPerformingCampaign(performances: any[]) {
    return performances.reduce((best, current) => {
      const bestROAS = best.calculatedMetrics.returnOnAdSpend;
      const currentROAS = current.calculatedMetrics.returnOnAdSpend;
      return currentROAS > bestROAS ? current : best;
    });
  }

  private generateComparisonRecommendations(performances: any[]): string[] {
    const recommendations: string[] = [];
    
    const avgROAS = performances.reduce((sum, p) => 
      sum + p.calculatedMetrics.returnOnAdSpend, 0) / performances.length;
    
    const underperforming = performances.filter(p => 
      p.calculatedMetrics.returnOnAdSpend < avgROAS * 0.8);
    
    if (underperforming.length > 0) {
      recommendations.push(`${underperforming.length} campaigns are underperforming - consider pausing or optimizing`);
    }
    
    const topPerformer = this.findBestPerformingCampaign(performances);
    recommendations.push(`Campaign ${topPerformer.campaignId} is the top performer - consider scaling similar campaigns`);
    
    return recommendations;
  }

  // Placeholder method
  private async getCampaignMetricsAPI(campaignId: string, dateRange: DateRange): Promise<CampaignMetrics> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }
}

interface DateRange {
  startDate: string;
  endDate: string;
}
```

## Best Practices

### 1. Campaign Lifecycle Management

```typescript
// ✅ Good: Implement complete campaign lifecycle
class CampaignLifecycleManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async launchCampaign(campaignId: string) {
    try {
      // Pre-launch validation
      const campaign = await this.getCampaignAPI(campaignId);
      const validationErrors = this.validateCampaignForLaunch(campaign);
      
      if (validationErrors.length > 0) {
        throw new Error(`Cannot launch campaign: ${validationErrors.join(', ')}`);
      }

      // Update status to active
      await this.updateCampaignStatusAPI(campaignId, 'ACTIVE');
      
      // Initialize tracking
      await this.initializeCampaignTracking(campaignId);
      
      console.log(`Campaign ${campaignId} launched successfully`);
    } catch (error) {
      console.error('Campaign launch failed:', error);
      throw error;
    }
  }

  private validateCampaignForLaunch(campaign: Campaign): string[] {
    const errors: string[] = [];
    
    if (campaign.status !== 'SCHEDULED') {
      errors.push('Campaign must be in scheduled status');
    }
    
    if (new Date(campaign.startDate) > new Date()) {
      errors.push('Campaign start date has not arrived');
    }
    
    if (!campaign.targetAudience.segments.length) {
      errors.push('Campaign must have target audience segments');
    }
    
    if (!campaign.channels.length) {
      errors.push('Campaign must have at least one channel');
    }
    
    return errors;
  }

  private async initializeCampaignTracking(campaignId: string) {
    // Initialize tracking pixels, UTM parameters, etc.
    console.log(`Initialized tracking for campaign ${campaignId}`);
  }

  // Placeholder methods
  private async getCampaignAPI(campaignId: string): Promise<Campaign> {
    throw new Error('Method not implemented');
  }

  private async updateCampaignStatusAPI(campaignId: string, status: CampaignStatus): Promise<void> {
    throw new Error('Method not implemented');
  }
}
```

### 2. A/B Testing

```typescript
// ✅ Good: Implement A/B testing for campaigns
class CampaignABTesting {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async createABTest(testConfig: ABTestConfig) {
    try {
      const variants = await Promise.all([
        this.createCampaignVariant(testConfig.variantA),
        this.createCampaignVariant(testConfig.variantB)
      ]);

      const abTest = {
        id: this.generateTestId(),
        name: testConfig.name,
        variants,
        trafficSplit: testConfig.trafficSplit || { A: 50, B: 50 },
        startDate: testConfig.startDate,
        endDate: testConfig.endDate,
        status: 'ACTIVE'
      };

      console.log('A/B test created:', abTest.id);
      return abTest;
    } catch (error) {
      console.error('A/B test creation failed:', error);
      throw error;
    }
  }

  async analyzeABTestResults(testId: string) {
    try {
      const test = await this.getABTestAPI(testId);
      const results = await Promise.all(
        test.variants.map(variant => this.getCampaignPerformanceAPI(variant.campaignId))
      );

      const analysis = {
        testId,
        winner: this.determineWinner(results),
        confidenceLevel: this.calculateConfidence(results),
        recommendations: this.generateABTestRecommendations(results)
      };

      return analysis;
    } catch (error) {
      console.error('A/B test analysis failed:', error);
      throw error;
    }
  }

  private determineWinner(results: any[]): string {
    return results.reduce((winner, current, index) => {
      const winnerROAS = results[winner].calculatedMetrics.returnOnAdSpend;
      const currentROAS = current.calculatedMetrics.returnOnAdSpend;
      return currentROAS > winnerROAS ? index : winner;
    }, 0) === 0 ? 'A' : 'B';
  }

  private calculateConfidence(results: any[]): number {
    // Simplified confidence calculation
    const [a, b] = results;
    const diff = Math.abs(a.metrics.conversionRate - b.metrics.conversionRate);
    return Math.min(95, diff * 10); // Simplified calculation
  }

  private generateABTestRecommendations(results: any[]): string[] {
    const recommendations: string[] = [];
    const winner = this.determineWinner(results);
    const confidence = this.calculateConfidence(results);

    if (confidence > 90) {
      recommendations.push(`Variant ${winner} is the clear winner with ${confidence}% confidence`);
      recommendations.push(`Scale variant ${winner} and pause the other variant`);
    } else {
      recommendations.push('Results are not statistically significant - continue testing');
    }

    return recommendations;
  }

  private generateTestId(): string {
    return 'test_' + Math.random().toString(36).substr(2, 9);
  }

  private async createCampaignVariant(variantConfig: any): Promise<any> {
    throw new Error('Method not implemented');
  }

  private async getABTestAPI(testId: string): Promise<any> {
    throw new Error('Method not implemented');
  }

  private async getCampaignPerformanceAPI(campaignId: string): Promise<any> {
    throw new Error('Method not implemented');
  }
}

interface ABTestConfig {
  name: string;
  variantA: any;
  variantB: any;
  trafficSplit?: { A: number; B: number };
  startDate: string;
  endDate: string;
}
```

---

**Next Steps:**
- [Image Service](./image.md) - Image and media management
- [Upload Service](./upload.md) - File upload handling
- [Portal Service](./portal.md) - Administrative portal operations
