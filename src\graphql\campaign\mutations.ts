import gql from "graphql-tag";

export const ADD_CUSTOMER_ID_INTO_VOUCHER = gql`
  mutation AddCustomerToVoucher(
    $partyId: String!
    $voucherCode: String!
    $userId: String!
    $affiliateId: String
  ) {
    addCustomerToVoucher(
      partyId: $partyId
      voucherCode: $voucherCode
      userId: $userId
      affiliateId: $affiliateId
    ) {
      id
      customerId
      campaignActionId
      campaignId
      partyId
      voucherCode
      voucherType
      status
      discountAmount
      discountPercent
      usageLimitPerVoucher
    }
  }
`;
