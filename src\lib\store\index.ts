// src/lib/store/index.ts
import { Service } from "../serviceSDK";
import type { StoreDTO } from "../../types/store";

/**
 * Service class for managing store-related operations.
 */
export class StoreService extends Service {
  /**
   * Constructs a new StoreService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  setToken(token: string) {
    this.token = token;
  }
  /**
   * Creates a store channel.
   *
   * @param storeChannelData - The data for creating the store channel.
   * @returns A promise that resolves to the created store channel response.
   * @throws If an error occurs while creating the store channel.
   */
  async createStoreChannel(storeChannelData: any) {
    const endpoint = `/store-channels/${this.orgId}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method, {
        ...storeChannelData,
        partyId: this.orgId,
      });
      return response;
    } catch (error) {
      console.log(`Error in createStoreChannel: ${error}`);
      throw error;
    }
  }

  /**
   * Gets available store channels for an employee.
   *
   * @param empId - The employee ID.
   * @returns A promise that resolves to the available store channels.
   * @throws If an error occurs while fetching the store channels.
   */
  async getAvailableStoreChannels(empId: string) {
    const endpoint = `/store-channels/LONGVAN/available/${empId}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getAvailableStoreChannels: ${error}`);
      throw error;
    }
  }

}
