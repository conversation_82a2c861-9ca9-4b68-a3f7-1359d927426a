import sdk from "../../setup";

describe("WarehouseService", () => {
  let warehouseService: any;
  beforeAll(() => {
    warehouseService = sdk.warehouse;
  });
  it("check inventory", async () => {
    const warehouseId = "1704257227604416";
    const listProduct = [{ productId: "121094", variantId: "121096", sku: "" }];
    try {
      const response = await warehouseService.getInventory(
        warehouseId,
        listProduct
      );
      console.log(response);

      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
});
