import { Service } from "../serviceSDK";
import {
  PAYMENTS_BY_ORDERS,
  PAYMENT_METHODS,
  GET_PAYMENT_METHOD_TYPES,
  PAYMENT_INFO,
  PAYMENT_STATUS,
  GW_CONFIG_DETAIL,
} from "../../graphql/paymentV2/queries";
import {
  CANCEL_PAYMENT,
  CONFIRM_PAYMENT_SUCCESS_MANUAL,
  CONFIRM_TO_GATEWAY,
} from "../../graphql/paymentV2/mutations";
export class PaymentServiceV2 extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async paymentsByOrders(orderIds: [string]) {
    const query = PAYMENTS_BY_ORDERS;
    const variables = {
      orderIds: orderIds,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.paymentsByOrders;
    } catch (error) {
      throw error;
    }
  }
  async cancelPayment(paymentId: string, reason: string) {
    const mutation = CANCEL_PAYMENT;
    const variables = {
      paymentId: paymentId,
      reason: reason,
    };
    try {
      const respone = await this.graphqlMutationV3(mutation, variables);
      return respone.cancelPayment;
    } catch (error) {
      throw error;
    }
  }
  async paymentMethods() {
    const query = PAYMENT_METHODS;
    const variables = {
      partnerCode: this.orgId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.paymentMethods;
    } catch (error) {
      throw error;
    }
  }
  async getPaymentMethodTypes() {
    const query = GET_PAYMENT_METHOD_TYPES;
    const variables = {
      partnerId: this.orgId,
      storeId: this.storeId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.getPaymentMethodTypes;
    } catch (error) {
      throw error;
    }
  }
  async paymentInfo(paymentId: string) {
    const query = PAYMENT_INFO;
    const variables = {
      paymentId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.paymentInfo;
    } catch (error) {
      throw error;
    }
  }
  async confirmToGateway(
    paymentId: string,
    methodCode: string,
    returnUrl: string
  ) {
    const mutation = CONFIRM_TO_GATEWAY;
    const variables = {
      paymentId,
      methodCode,
      returnUrl,
    };
    try {
      const response = await this.graphqlMutationV3(mutation, variables);
      return response.confirmToGateway;
    } catch (error) {
      throw error;
    }
  }
  async paymentStatus(paymentId: string) {
    const query = PAYMENT_STATUS;
    const variables = {
      paymentId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.paymentStatus;
    } catch (error) {
      throw error;
    }
  }
  async gwConfigDetail(configId: string) {
    const query = GW_CONFIG_DETAIL;
    const variables = {
      configId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.gwConfigDetail;
    } catch (error) {
      throw error;
    }
  }

  async confirmPaymentSuccessManual(
    paymentId: string,
    transactionNo: string,
    note: string,
    confirmBy: string
  ) {
    const mutation = CONFIRM_PAYMENT_SUCCESS_MANUAL;
    const variables = {
      paymentId: paymentId,
      transactionNo: transactionNo,
      note: note,
      confirmBy: confirmBy,
    };
    try {
      const response = await this.graphqlMutationV3(mutation, variables);
      return response.confirmPaymentSuccessManual;
    } catch (error) {
      throw error;
    }
  }
}
