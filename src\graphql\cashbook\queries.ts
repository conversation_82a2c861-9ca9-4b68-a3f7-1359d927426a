import { gql } from "graphql-tag";

export const SEARCH_TRANSACTIONS = gql`
  query SearchTransactions(
    $partnerId: String!
    $keyword: String
    $dateFrom: Long
    $dateTo: Long
    $currentPage: Int
    $pageSize: Int
  ) {
    searchTransactions(
      partnerId: $partnerId
      keyword: $keyword
      dateFrom: $dateFrom
      dateTo: $dateTo
      currentPage: $currentPage
      pageSize: $pageSize
    ) {
      total
      index
      maxResult
      resultList {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        partnerId
        extId
        transactionId
        paymentGatewayType
        bankCode
        bankAccountNumber
        extBankAccountNumber
        extBank
        amount
        currencyCode
        description
        timeTransaction
        status
        gateway
        paymentId
        paymentAmount
        paymentConfirmStatus
        paymentConfirmNote
        accountTransactionId
        orderId
        invoiceId
        cashAccountId
        purpose
        customAttributes
      }
    }
  }
`;
export const GET_CASHBOOK_TRANSACTION_DETAIL = gql`
  query GetCashbookTransactionDetail(
    $partnerId: String!
    $cashTransactionId: String!
  ) {
    getCashbookTransactionDetail(
      partnerId: $partnerId
      cashTransactionId: $cashTransactionId
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      partnerId
      extId
      transactionId
      paymentGatewayType
      bankCode
      bankAccountNumber
      extBankAccountNumber
      extBank
      amount
      currencyCode
      description
      timeTransaction
      status
      gateway
      paymentId
      paymentAmount
      paymentConfirmStatus
      paymentConfirmNote
      accountTransactionId
      orderId
      invoiceId
      cashAccountId
      purpose
      customAttributes
    }
  }
`;
