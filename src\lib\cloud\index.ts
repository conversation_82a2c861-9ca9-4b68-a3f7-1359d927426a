import {
  CHANGE_SERVICE_NAME,
  CREATE_USER_MAIL_HOSTING,
  DELETE_USER_MAIL_HOSTING,
  UPDATE_DNS,
  UPDATE_DOMAIN_NAME,
  UPDATE_PASSWORD,
  UPDATE_USER_NAME,
  UPDATE_USER_PASSWORD,
} from "../../graphql/cloud/mutations";
import {
  GET_MAIL_RESOURCE,
  GET_USER_MAIL_HOSTING,
  SEARCH_SERVICE,
  SERVICE_DETAIL,
  SERVICE_TYPE,
} from "../../graphql/cloud/queries";
import {
  CreateUserMailHosting,
  DeleteUserPassword,
  Filter,
  UpdateInforMationService,
  UpdateUserPassword,
} from "../../types/cloud";
import { Service } from "../serviceSDK";

export class CloudService extends Service {
  /**
   * Constructs a new OrderService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }

  async serviceDetail(serviceId: string) {
    const query = SERVICE_DETAIL;
    const variables = {
      serviceId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.serviceDetail;
    } catch (error) {
      console.log(`Error in serviceDetail: ${error}`);
      throw error;
    }
  }

  async getMailResource(serviceId: string) {
    const query = GET_MAIL_RESOURCE;
    const variables = {
      serviceId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getMailResource;
    } catch (error) {
      console.log(`Error in getMailResource: ${error}`);
      throw error;
    }
  }

  async changeServiceName(serviceId: string, updateBy: string, name: string) {
    const mutation = CHANGE_SERVICE_NAME;
    const variables = {
      serviceId,
      updateBy,
      name,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.changeServiceName;
    } catch (error) {
      console.log(`Error in changeServiceName: ${error}`);
      throw error;
    }
  }

  async getUserMailHosting(serviceId: string) {
    const query = GET_USER_MAIL_HOSTING;
    const variables = {
      serviceId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getUserMailHosting;
    } catch (error) {
      console.log(`Error in getUserMailHosting: ${error}`);
      throw error;
    }
  }

  async createUserMailHosting(payload: CreateUserMailHosting) {
    const mutation = CREATE_USER_MAIL_HOSTING;
    const variables = {
      serviceId: payload.serviceId,
      username: payload.username,
      password: payload.password,
      fullName: payload.fullName,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createUserMailHosting;
    } catch (error) {
      console.log(`Error in createUserMailHosting: ${error}`);
      throw error;
    }
  }

  async updateUserPassword(payload: UpdateUserPassword) {
    const mutation = UPDATE_USER_PASSWORD;
    const variables = {
      serviceId: payload.serviceId,
      username: payload.username,
      password: payload.password,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateUserPassword;
    } catch (error) {
      console.log(`Error in updateUserPassword: ${error}`);
      throw error;
    }
  }

  async deleteUserMailHosting(payload: DeleteUserPassword) {
    const mutation = DELETE_USER_MAIL_HOSTING;
    const variables = {
      serviceId: payload.serviceId,
      username: payload.username,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.deleteUserMailHosting;
    } catch (error) {
      console.log(`Error in deleteUserMailHosting: ${error}`);
      throw error;
    }
  }

  async updateDomainName(payload: UpdateInforMationService) {
    const mutation = UPDATE_DOMAIN_NAME;
    const variables = {
      serviceId: payload.serviceId,
      updateBy: payload.useUpdate,
      updateData: payload.valueUpdate,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateDomainName;
    } catch (error) {
      console.log(`Error in updateDomainName: ${error}`);
      throw error;
    }
  }

  async updateUsername(payload: UpdateInforMationService) {
    const mutation = UPDATE_USER_NAME;
    const variables = {
      serviceId: payload.serviceId,
      updateBy: payload.useUpdate,
      updateData: payload.valueUpdate,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateUsername;
    } catch (error) {
      console.log(`Error in updateUsername: ${error}`);
      throw error;
    }
  }

  async updatePassword(payload: UpdateInforMationService) {
    const mutation = UPDATE_PASSWORD;
    const variables = {
      serviceId: payload.serviceId,
      updateBy: payload.useUpdate,
      updateData: payload.valueUpdate,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updatePassword;
    } catch (error) {
      console.log(`Error in updatePassword: ${error}`);
      throw error;
    }
  }

  async serviceTypes() {
    const query = SERVICE_TYPE;
    const variables = {};
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.serviceTypes;
    } catch (error) {
      console.log(`Error in serviceTypes: ${error}`);
      throw error;
    }
  }

  async searchService(filter: Filter) {
    const query = SEARCH_SERVICE;
    const variables = {
      filter,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.searchService;
    } catch (error) {
      console.log(`Error in searchService: ${error}`);
      throw error;
    }
  }

  async updateDNS(payload: {}) {
    const mutation = UPDATE_DNS;
    const variables = {
      ...payload,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateDNS;
    } catch (error) {
      console.log(`Error in updateDNS: ${error}`);
      throw error;
    }
  }
}
