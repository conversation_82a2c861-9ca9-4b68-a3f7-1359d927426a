import sdk from "../../setup";

describe("PaymentService", () => {
  let paymentService: any;

  beforeAll(() => {
    paymentService = sdk.payment;
  });

  it("getPaymentMethod integration test", async () => {
    const paymentMethod = await paymentService.getPaymentMethod();
    console.log("🚀 ~ it ~ paymentMethod:", paymentMethod);
    // expect(product).toHaveProperty("id", "104329");
  });
  // it("createPaymentOrder integration test", async () => {
  //   const data = {
  //     orderId: "20.106468", // mã đơn hàng
  //     paymentMethod: "momo", // phương thức thanh toán (code trong api lấy pttt)
  //     appliedAmount: 290000, // tiền thành toán
  //     payDate: "1694741297259", // ngày thanh toán
  //     source: "ORDER_SOURCE,", // nguồn thanh toán
  //     returnUrl: "hpost…ttps://dev.", //trang sau khi thanh toán thành công
  //     paymentType: "ONLINE",
  //     createBy: "20.123", // mã người tạo
  //   };
  //   const paymentOrder = await paymentService.createPaymentOrder(data);
  //   console.log("🚀 ~ it ~ paymentOrder:", paymentOrder);
  // });

  // Add more integration tests for other methods...
});
