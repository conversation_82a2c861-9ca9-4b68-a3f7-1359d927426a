import { Service } from "../serviceSDK";
export class UploadService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async uploadImage(imageCode: string) {
    const endpoint = "";
    const method: "POST" = "POST";
    try {
      const formData = new FormData();
      formData.append("uploadFile", imageCode);
      formData.append("path", "public");
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        formData
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
