Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFA9450000 ntdll.dll
7FFFA8CA0000 KERNEL32.DLL
7FFFA66B0000 KERNELBASE.dll
7FFFA8120000 USER32.dll
7FFFA6600000 win32u.dll
7FFFA90C0000 GDI32.dll
7FFFA6A90000 gdi32full.dll
7FFFA6560000 msvcp_win.dll
7FFFA6F20000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFA90F0000 advapi32.dll
7FFFA9350000 msvcrt.dll
7FFFA71A0000 sechost.dll
7FFFA70C0000 bcrypt.dll
7FFFA8E80000 RPCRT4.dll
7FFFA5D70000 CRYPTBASE.DLL
7FFFA6630000 bcryptPrimitives.dll
7FFFA9080000 IMM32.DLL
