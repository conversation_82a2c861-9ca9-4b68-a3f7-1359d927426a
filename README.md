# Longvan Storefront JavaScript Client SDK

[![npm version](https://badge.fury.io/js/%40longvansoftware%2Fstorefront-js-client.svg)](https://badge.fury.io/js/%40longvansoftware%2Fstorefront-js-client)
[![TypeScript](https://img.shields.io/badge/%3C%2F%3E-TypeScript-%230074c1.svg)](http://www.typescriptlang.org/)
[![License: ISC](https://img.shields.io/badge/License-ISC-blue.svg)](https://opensource.org/licenses/ISC)

A comprehensive TypeScript/JavaScript SDK for integrating with the Longvan e-commerce platform. This SDK provides easy-to-use interfaces for managing products, orders, users, payments, and other e-commerce functionalities.

## 🚀 Quick Start

```bash
npm install @longvansoftware/storefront-js-client
```

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

const sdk = new SDK('your-org-id', 'your-store-id', 'dev');
sdk.setToken('your-access-token');

// Ready to use!
const products = await sdk.product.getSimpleProducts({ currentPage: 1, maxResult: 10 });
```

## 📚 Documentation

### 🎯 Getting Started
- **[Installation & Setup](./docs/getting-started/installation.md)** - Complete installation guide
- **[Quick Start Guide](./docs/getting-started/quick-start.md)** - Get running in 5 minutes
- **[Configuration](./docs/getting-started/configuration.md)** - Environment and SDK configuration
- **[Authentication](./docs/getting-started/authentication.md)** - Authentication setup and patterns

### 🔧 Core Services
- **[Authentication Service](./docs/services/auth.md)** - User authentication and authorization
- **[Product Service](./docs/services/product.md)** - Product catalog management
- **[Order Service](./docs/services/order.md)** - Order lifecycle management
- **[User Service](./docs/services/user.md)** - Customer and user management
- **[Payment Service](./docs/services/payment.md)** - Payment processing and invoicing
- **[CRM Service](./docs/services/crm.md)** - Customer relationship management

### 🛠️ Extended Services
- **[Warehouse Service](./docs/services/warehouse.md)** - Inventory management
- **[Computing Service](./docs/services/computing.md)** - Cloud computing services
- **[Campaign Service](./docs/services/campaign.md)** - Marketing campaigns
- **[Image Service](./docs/services/image.md)** - Image and media management
- **[Upload Service](./docs/services/upload.md)** - File upload handling

### 📖 Advanced Topics
- **[Error Handling](./docs/advanced/error-handling.md)** - Robust error handling patterns
- **[TypeScript Support](./docs/advanced/typescript.md)** - Full TypeScript integration
- **[Testing](./docs/advanced/testing.md)** - Testing your integration
- **[Performance](./docs/advanced/performance.md)** - Optimization and best practices

### 💡 Examples & Patterns
- **[E-commerce Flow](./docs/examples/ecommerce-flow.md)** - Complete workflow examples
- **[Authentication Patterns](./docs/examples/auth-patterns.md)** - Auth implementation patterns
- **[Product Management](./docs/examples/product-management.md)** - Product handling examples
- **[Order Processing](./docs/examples/order-processing.md)** - Order workflow examples

### 🔍 API Reference
- **[Complete API Reference](./docs/api/README.md)** - Full API documentation
- **[Type Definitions](./docs/api/types.md)** - TypeScript type definitions
- **[Interfaces](./docs/api/interfaces.md)** - Interface specifications

## 🏗️ Architecture Overview

```
Longvan SDK
├── 🔐 Authentication Layer
├── 🛍️ Core E-commerce Services
│   ├── Product Management
│   ├── Order Processing
│   ├── User Management
│   └── Payment Processing
├── 🔧 Extended Services
│   ├── Warehouse & Inventory
│   ├── CRM & Customer Support
│   ├── Marketing & Campaigns
│   └── Media & File Management
└── 🚀 Infrastructure
    ├── GraphQL Client
    ├── REST API Client
    ├── Error Handling
    └── TypeScript Support
```

## ✨ Key Features

- **🎯 Type-Safe**: Full TypeScript support with comprehensive type definitions
- **🔌 Multi-Protocol**: Supports both GraphQL and REST APIs
- **🔐 Secure**: Built-in authentication and token management
- **⚡ Performance**: Optimized for speed with caching and batching
- **🛠️ Developer-Friendly**: Intuitive API design with excellent IntelliSense
- **📱 Cross-Platform**: Works in Node.js, browsers, and React Native
- **🧪 Well-Tested**: Comprehensive test suite with high coverage
- **📖 Well-Documented**: Extensive documentation with real-world examples

## 🚀 Installation

### Using npm
```bash
npm install @longvansoftware/storefront-js-client
```

### Using yarn
```bash
yarn add @longvansoftware/storefront-js-client
```

### Using pnpm
```bash
pnpm add @longvansoftware/storefront-js-client
```

## ⚡ Quick Example

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

// Initialize SDK
const sdk = new SDK('your-org-id', 'your-store-id', 'dev');

// Authenticate user
const loginResponse = await sdk.auth.login({
  username: '<EMAIL>',
  password: 'password123'
});
sdk.setToken(loginResponse.accessToken);

// Browse products
const products = await sdk.product.getSimpleProducts({
  keyword: 'laptop',
  currentPage: 1,
  maxResult: 20
});

// Create order
const order = await sdk.order.createOrder({
  customer_id: loginResponse.partyId,
  line_items: [{
    product_id: products[0].id,
    quantity: 1,
    input_price: products[0].price
  }]
}, 'web', false, loginResponse.partyId);

console.log('Order created:', order.id);

// Manage stores
const newStore = await sdk.store.createStore('FOX', 'My New Store');
const channels = await sdk.store.getStoreChannelByEmpId('emp-123');
```

## 📦 TypeScript Support

Import types for full TypeScript support:

```typescript
// Import specific types
import type {
  Product,
  ProductFilterOptions,
} from '@longvansoftware/storefront-js-client/dist/src/types/product';

import type {
  StoreDTO,
  CreateStoreRequest,
  StoreConfiguration,
} from '@longvansoftware/storefront-js-client/dist/src/types/store';

import type {
  LoginRequest,
  LoginResponse,
} from '@longvansoftware/storefront-js-client/dist/src/types/auth';

// Use with full type safety
const products: Product[] = await sdk.product.getSimpleProducts({
  keyword: 'laptop',
  currentPage: 1,
  maxResult: 10
});

const stores: StoreDTO[] = await sdk.store.getStoreChannelByEmpId('emp-123');
```

## 🌟 What's New in v2.9.0

- ✨ Enhanced TypeScript support with stricter types
- 🚀 Improved error handling and debugging capabilities
- 🔧 New utility services for better platform integration
- ⚡ Performance optimizations and caching improvements
- 🔒 Updated dependencies and security enhancements
- 📖 Comprehensive documentation with real-world examples

## 🤝 Community & Support

- **📖 Documentation**: [Complete Documentation](./docs/README.md)
- **🐛 Issues**: [GitLab Issues](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues)
- **💬 Discussions**: [GitLab Discussions](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues)
- **📧 Support**: Contact the Longvan development team

## 🚀 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/contributing/README.md) for details on:

- 🔧 Setting up the development environment
- 📝 Code style and conventions
- 🧪 Running tests and quality checks
- 📤 Submitting pull requests

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](./LICENSE) file for details.

---

**Ready to get started?**
- 🚀 [Quick Start Guide](./docs/getting-started/quick-start.md)
- 📖 [Complete Documentation](./docs/README.md)
- 🔍 [API Reference](./docs/api/README.md)

**Need help?** Open an issue on [GitLab](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues) or check our [documentation](./docs/README.md).