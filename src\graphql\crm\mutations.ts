import { gql } from "graphql-tag";

export const ADD_OPPORTUNITY_MUTATION = gql`
  mutation AddOpportunity(
    $partyId: String!
    $addOpportunityRequest: AddOpportunityRequest
    $performerId: String!
  ) {
    addOpportunity(
      partyId: $partyId
      addOpportunityRequest: $addOpportunityRequest
      performerId: $performerId
    ) {
      goal
      campaignId
      valueReal
      valueExpect
      successRate
      referName
      referPhone
      referEmail
      id
      createdBy
      ownerId
      workEffortTypeId
      partyId
      name
      description
      parentId
      status
      stmId
      createdStamp
      updatedStamp
      endDateExpect
      priorityName
      targetId
      targetType
      targetUrl
      extSource
      connectorId
      processResult
    }
  }
`;

export const UPDATE_STATUS_ATTACHMENT_BY_ID = gql`
  mutation UpdateStatusAttachmentById(
    $performerId: String!
    $attachmentId: String!
    $status: AttachmentStatus!
  ) {
    updateStatusAttachmentById(
      performerId: $performerId
      attachmentId: $attachmentId
      status: $status
    ) {
      id
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partyId
      path
      srcId
      srcName
      srcPath
      srcConfigPathId
      name
      fileType
      type
      status
      referId
    }
  }
`;

export const UPDATE_WORK_EFFORT_DESCRIPTION = gql`
  mutation UpdateWorkEffortDescription(
    $performerId: String!
    $workEffortId: String!
    $description: String!
  ) {
    updateWorkEffortDescription(
      performerId: $performerId
      workEffortId: $workEffortId
      description: $description
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
    }
  }
`;

export const UPDATE_WORK_EFFORT_NAME = gql`
  mutation UpdateWorkEffortName(
    $partyId: String!
    $performerId: String!
    $workEffortId: String!
    $newName: String!
  ) {
    updateWorkEffortName(
      partyId: $partyId
      performerId: $performerId
      workEffortId: $workEffortId
      newName: $newName
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
      processPipeline {
        id
        name
      }
      attachments {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        partyId
        path
        srcId
        srcName
        srcPath
        srcConfigPathId
        name
        fileType
        type
        status
        referId
      }
      subTasks {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        name
        partyId
        targetId
        targetType
        targetUrl
        description
        status
        parentId
        workEffortTypeId
        stmId
        workflowId
        endDateExpect
        endDateActual
        startDateActual
        startDateExpect
        source
        owner {
          id
          fullName
          name
          type
          phone
          email
        }
        priorityName
        priorityValue
        connectorId
        mode
        partyGroupIds
        tagIds
        processResult
      }
      actionLink {
        name
        uri
        type
        partyId
        fromCollection
        toCollection
        group
        params
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
      }
    }
  }
`;

export const UPDATE_WORK_EFFORT_STATUS = gql`
  mutation UpdateWorkEffortStatus(
    $partyId: String!
    $performerId: String!
    $workEffortId: String!
    $source: String!
    $status: String!
  ) {
    updateWorkEffortStatus(
      partyId: $partyId
      performerId: $performerId
      workEffortId: $workEffortId
      source: $source
      status: $status
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      priorityName
      priorityValue
      extSource
      extSourceTopicId
      extSourceSocialAppId
      extSourceSupportChannelType
      extSourceSocialChannelType
      extSourceSocialAppName
      extSourceTopicUrl
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
    }
  }
`;

export const ADD_ATTACHMENT_FOR_WORK_EFFORT = gql`
  mutation (
    $partyId: String!
    $performerId: String!
    $workEffortId: String!
    $addAttachmentRequest: [AddAttachmentRequest]
  ) {
    addAttachmentForWorkEffort(
      partyId: $partyId
      performerId: $performerId
      workEffortId: $workEffortId
      addAttachmentRequest: $addAttachmentRequest
    ) {
      id
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partyId
      path
      srcId
      srcName
      srcPath
      srcConfigPathId
      name
      fileType
      type
      referId
    }
  }
`;

export const ADD_TICKED = gql`
  mutation AddTicket(
    $partyId: String!
    $performerId: String!
    $addTicketRequest: AddTicketRequest!
    $addAttachmentRequest: [AddAttachmentRequest]
  ) {
    addTicket(
      partyId: $partyId
      performerId: $performerId
      addTicketRequest: $addTicketRequest
      addAttachmentRequest: $addAttachmentRequest
    ) {
      id
      partyId
      name
      description
      status
      parentId
      stmId
      createdStamp
      updatedStamp
      endDateExpect
      priorityName
      targetId
      targetType
      extSource
      connectorId
    }
  }
`;

export const ADD_COMMENT = gql`
  mutation AddComment(
    $partyId: String!
    $performerId: String!
    $format: CommentFormat!
    $content: String
    $referId: String!
    $addAttachmentRequest: [AddAttachmentRequest]
  ) {
    addComment(
      partyId: $partyId
      performerId: $performerId
      format: $format
      content: $content
      referId: $referId
      addAttachmentRequest: $addAttachmentRequest
    ) {
      id
      content
    }
  }
`;
export const CREATE_WORK_EFFORT = gql`
  mutation CreateWorkEffort(
    $partnerId: String!
    $createdBy: String!
    $name: String
    $description: String
    $workEffortTypeId: String!
    $source: String!
    $attributes: JSON
    $addAttachmentRequest: [AddAttachmentRequest]
    $parentId: String
  ) {
    createWorkEffort(
      partnerId: $partnerId
      createdBy: $createdBy
      name: $name
      description: $description
      workEffortTypeId: $workEffortTypeId
      source: $source
      attributes: $attributes
      addAttachmentRequest: $addAttachmentRequest
      parentId: $parentId
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
      processStatus
      subTasks {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        name
        partyId
        targetId
        targetType
        targetUrl
        description
        status
        parentId
        workEffortTypeId
        stmId
        workflowId
        endDateExpect
        endDateActual
        startDateActual
        startDateExpect
        source
        owner {
          id
          fullName
          name
          type
          phone
          email
        }
        priorityName
        priorityValue
        connectorId
        mode
        partyGroupIds
        tagIds
        processResult
        processStatus
        processPipeline {
          id
          name
        }
        attachments {
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
          partyId
          path
          srcId
          srcName
          srcPath
          srcConfigPathId
          name
          fileType
          type
          status
          referId
        }
        subTasks {
          id
          createdStamp
          updatedStamp
          createdBy
          updatedBy
          name
          partyId
          targetId
          targetType
          targetUrl
          description
          status
          parentId
          workEffortTypeId
          stmId
          workflowId
          endDateExpect
          endDateActual
          startDateActual
          startDateExpect
          source
          owner {
            id
            fullName
            name
            type
            phone
            email
          }
          priorityName
          priorityValue
          connectorId
          mode
          partyGroupIds
          tagIds
          processResult
          processStatus
        }
        actionLink {
          name
          uri
          type
          partyId
          fromCollection
          toCollection
          group
          params
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
        }
      }
      attachments {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        partyId
        path
        srcId
        srcName
        srcPath
        srcConfigPathId
        name
        fileType
        type
        status
        referId
      }
      processPipeline {
        id
        name
      }
    }
  }
`;
export const UPDATE_WORK_EFFORT_PROCESS_STATUS = gql`
  mutation UpdateWorkEffortProcessStatus(
    $workEffortId: String!
    $processStatus: String!
    $performerId: String!
  ) {
    updateWorkEffortProcessStatus(
      workEffortId: $workEffortId
      processStatus: $processStatus
      performerId: $performerId
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
      actionLink {
        name
        uri
        type
        partyId
        fromCollection
        toCollection
        group
        params
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
      }
      subTasks {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        name
        partyId
        targetId
        targetType
        targetUrl
        description
        status
        parentId
        workEffortTypeId
        stmId
        workflowId
        endDateExpect
        endDateActual
        startDateActual
        startDateExpect
        source
        owner {
          id
          fullName
          name
          type
          phone
          email
        }
        priorityName
        priorityValue
        connectorId
        mode
        partyGroupIds
        tagIds
        processResult
      }
      attachments {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        partyId
        path
        srcId
        srcName
        srcPath
        srcConfigPathId
        name
        fileType
        type
        status
        referId
      }
      processPipeline {
        id
        name
      }
    }
  }
`;
export const CREATE_CONNECTOR = gql`
  mutation CreateConnector(
    $resourceId: String!
    $resourceType: String!
    $description: String
    $type: String!
    $createdBy: String!
  ) {
    createConnector(
      resourceId: $resourceId
      resourceType: $resourceType
      description: $description
      type: $type
      createdBy: $createdBy
    )
  }
`;
export const ADD_TAG = gql`
  mutation AddTag(
    $connectorId: String!
    $tagTitle: String!
    $tagId: String
    $addedBy: String!
  ) {
    addTag(
      connectorId: $connectorId
      tagTitle: $tagTitle
      tagId: $tagId
      addedBy: $addedBy
    )
  }
`;
export const UPDATE_CONNECTOR_DESCRIPTION = gql`
  mutation UpdateConnectorDescription(
    $connectorId: String!
    $description: String!
    $updatedBy: String!
  ) {
    updateConnectorDescription(
      connectorId: $connectorId
      description: $description
      updatedBy: $updatedBy
    )
  }
`;
export const REMOVE_TAG = gql`
  mutation RemoveTag(
    $connectorId: String!
    $tagId: String!
    $removedBy: String!
  ) {
    removeTag(connectorId: $connectorId, tagId: $tagId, removedBy: $removedBy)
  }
`;
export const CLOSE_TOPIC = gql`
  mutation CloseTopic($id: String!, $updatedBy: String) {
    closeTopic(id: $id, updatedBy: $updatedBy) {
      id
      status
      name
      communicationChannel
      accountableId
      accountableName
      createdStamp
      threadId
      roomId
      channelId
      channelType
    }
  }
`;
export const CREATE_TOPIC = gql`
  mutation CreateTopic(
    $socialAppId: String!
    $customerId: String!
    $message: String!
  ) {
    createTopic(
      socialAppId: $socialAppId
      customerId: $customerId
      message: $message
    ) {
      id
      status
      name
      communicationChannel
      customerId
      accountableId
      accountableName
      customerName
      createdStamp
      threadId
      roomId
    }
  }
`;
export const ADD_TOPIC_RELATED_RESOURCE = gql`
  mutation AddTopicRelatedResource(
    $topicId: String!
    $resourceId: String!
    $resourceType: String!
    $createdBy: String!
  ) {
    addTopicRelatedResource(
      topicId: $topicId
      resourceId: $resourceId
      resourceType: $resourceType
      createdBy: $createdBy
    )
  }
`;
export const CREATE_WORK_EFFORT_V2 = gql`
  mutation CreateWorkEffort(
    $partnerId: String!
    $createdBy: String!
    $name: String
    $description: String
    $workEffortTypeId: String!
    $source: String!
  ) {
    createWorkEffort(
      partnerId: $partnerId
      createdBy: $createdBy
      name: $name
      description: $description
      workEffortTypeId: $workEffortTypeId
      source: $source
    ) {
      id
      createdStamp
      updatedStamp
      createdBy
      updatedBy
      name
      partyId
      targetId
      targetType
      targetUrl
      description
      status
      parentId
      workEffortTypeId
      stmId
      workflowId
      endDateExpect
      endDateActual
      startDateActual
      startDateExpect
      source
      owner {
        id
        fullName
        name
        type
        phone
        email
      }
      priorityName
      priorityValue
      connectorId
      mode
      partyGroupIds
      tagIds
      processResult
      processStatus
      subTasks {
        id
        createdStamp
        updatedStamp
        createdBy
        updatedBy
        name
        partyId
        targetId
        targetType
        targetUrl
        description
        status
        parentId
        workEffortTypeId
        stmId
        workflowId
        endDateExpect
        endDateActual
        startDateActual
        startDateExpect
        source
        owner {
          id
          fullName
          name
          type
          phone
          email
        }
        priorityName
        priorityValue
        connectorId
        mode
        partyGroupIds
        tagIds
        processResult
        processStatus
        processPipeline {
          id
          name
        }
        attachments {
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
          partyId
          path
          srcId
          srcName
          srcPath
          srcConfigPathId
          name
          fileType
          type
          status
          referId
        }
        subTasks {
          id
          createdStamp
          updatedStamp
          createdBy
          updatedBy
          name
          partyId
          targetId
          targetType
          targetUrl
          description
          status
          parentId
          workEffortTypeId
          stmId
          workflowId
          endDateExpect
          endDateActual
          startDateActual
          startDateExpect
          source
          owner {
            id
            fullName
            name
            type
            phone
            email
          }
          priorityName
          priorityValue
          connectorId
          mode
          partyGroupIds
          tagIds
          processResult
          processStatus
        }
        actionLink {
          name
          uri
          type
          partyId
          fromCollection
          toCollection
          group
          params
          id
          createdStamp
          updatedStamp
          updatedBy
          createdBy
        }
      }
      attachments {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        partyId
        path
        srcId
        srcName
        srcPath
        srcConfigPathId
        name
        fileType
        type
        status
        referId
      }
      processPipeline {
        id
        name
      }
    }
  }
`;
