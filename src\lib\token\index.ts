import { Service } from "../serviceSDK";
export class AuthorizationService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async checkToken(orgId: string, token: string) {
    const endpoint = `/${orgId}/oauth2/api/v1/token/${token}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
