import { Service } from "../serviceSDK";

export class ZcaService extends Service {
  /**
   * Constructs a new OrderService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }
  async loginQR(sessionId: string, appId: string) {
    const endpoint = `/login?sessionId=${sessionId}&orgId=${this.orgId}&storeId=${this.storeId}&appId=${appId}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
