import { gql } from "graphql-tag";

export const CREATE_PAYMENT_ORDER_MUTATION = gql`
  mutation CreatePaymentOrder(
    $orgId: String!
    $orderId: String!
    $paymentMethod: String!
    $storeId: String!
    $source: String!
    $appliedAmount: BigDecimal
    $payDate: String!
    $returnUrl: String
    $paymentType: String!
    $createBy: String!
    $paymentInfo: String
    $attributes: JSON
  ) {
    createPaymentOrder(
      orgId: $orgId
      orderId: $orderId
      paymentMethod: $paymentMethod
      storeId: $storeId
      source: $source
      appliedAmount: $appliedAmount
      payDate: $payDate
      returnUrl: $returnUrl
      paymentType: $paymentType
      createBy: $createBy
      paymentInfo: $paymentInfo
      attributes: $attributes
    ) {
      code
      message
      data
      qrCodeUrl
      deeplink
      deeplinkMiniApp
      invoiceId
      orderId
      paymentId
    }
  }
`;
export const REQUEST_UNPUBLISH_VAT_INVOICE = gql`
  mutation RequestUnpublishVatInvoice(
    $partnerId: String!
    $sourceId: String!
    $sourceType: String!
    $signType: String
    $includeBuyerTaxCode: Boolean
    $buyerName: String
    $buyerTaxCode: String
    $buyerCompany: String
    $buyerAddress: String
    $sendMail: Boolean
    $receiverName: String
    $receiverEmail: String
    $byUser: String!
  ) {
    requestUnpublishVatInvoice(
      vatInvoiceRequest: {
        partnerId: $partnerId
        sourceId: $sourceId
        sourceType: $sourceType
        signType: $signType
        includeBuyerTaxCode: $includeBuyerTaxCode
        buyerName: $buyerName
        buyerTaxCode: $buyerTaxCode
        buyerCompany: $buyerCompany
        buyerAddress: $buyerAddress
        sendMail: $sendMail
        receiverName: $receiverName
        receiverEmail: $receiverEmail
      }
      byUser: $byUser
    ) {
      code
      message
      invoiceId
      sourceId
      sourceType
      previewLink
      lookupLink
    }
  }
`;
export const REQUEST_PUBLISH_VAT_INVOICE = gql`
  mutation RequestPublishVatInvoice(
    $partnerId: String!
    $sourceId: String!
    $sourceType: String!
    $signType: String
    $includeBuyerTaxCode: Boolean
    $buyerName: String
    $buyerTaxCode: String
    $buyerCompany: String
    $buyerAddress: String
    $sendMail: Boolean
    $receiverName: String
    $receiverEmail: String
    $byUser: String!
  ) {
    requestPublishVatInvoice(
      vatInvoiceRequest: {
        partnerId: $partnerId
        sourceId: $sourceId
        sourceType: $sourceType
        signType: $signType
        includeBuyerTaxCode: $includeBuyerTaxCode
        buyerName: $buyerName
        buyerTaxCode: $buyerTaxCode
        buyerCompany: $buyerCompany
        buyerAddress: $buyerAddress
        sendMail: $sendMail
        receiverName: $receiverName
        receiverEmail: $receiverEmail
      }
      byUser: $byUser
    ) {
      code
      message
      invoiceId
      sourceId
      sourceType
      previewLink
      lookupLink
    }
  }
`;
export const UPDATE_INVOICE_ITEM = gql`
  mutation UpdateInvoiceItem(
    $partnerId: String!
    $invoiceId: String!
    $updateInvoiceItemDTO: UpdateInvoiceItemDTO!
    $byUser: String!
  ) {
    updateInvoiceItem(
      partnerId: $partnerId
      invoiceId: $invoiceId
      updateInvoiceItemDTO: $updateInvoiceItemDTO
      byUser: $byUser
    ) {
      code
      message
      invoiceId
      sourceId
      sourceType
      previewLink
      lookupLink
    }
  }
`;
