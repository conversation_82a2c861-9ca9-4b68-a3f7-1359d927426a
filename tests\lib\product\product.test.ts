// tests/lib/product/product.test.ts

import sdk from "../../setup";

describe("ProductService", () => {
  let productService: any;

  beforeAll(() => {
    productService = sdk.product;
  });

  it("getProductById integration test", async () => {
    const product = await productService.getProductById("121085");
    console.log("product ne", product);
    expect(product).toHaveProperty("id", "121085");
  });

  it("getProductBySlug integration test", async () => {
    const product = await productService.getProductBySlug(
      "giay-sandal-2-quai-cao-3cm-m039-104329"
    );
    expect(product).toHaveProperty(
      "handle",
      "giay-sandal-2-quai-cao-3cm-m039-104329"
    );
  });

  it("getSimpleProducts integration test", async () => {
    const products = await productService.getSimpleProducts({
      maxResult: 10,
      category: "1808",
      currentPage: 1,
    });
    expect(products).toBeDefined();
  });

  it("getProductOption integration test", async () => {
    const products = await productService.getProductOption("120109");
    console.log("products", products);
    expect(products).toBeDefined();
  });
  it("getstores", async () => {
    const res = await productService.getStores();
    console.log("res", res);
    expect(res).toBeDefined();
  });
  // Add more integration tests for other methods...
});
