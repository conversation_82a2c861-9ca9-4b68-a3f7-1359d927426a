import { Service } from "../serviceSDK";
import {
  SEARCH_TRANSACTIONS,
  GET_CASHBOOK_TRANSACTION_DETAIL,
} from "../../graphql/cashbook/queries";
export class CashbookService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async searchTransactions(
    keyword: string,
    dateFrom: number,
    dateTo: number,
    currentPage: number,
    pageSize: number
  ) {
    const query = SEARCH_TRANSACTIONS;
    const variables = {
      partnerId: this.orgId,
      keyword,
      dateFrom,
      dateTo,
      currentPage,
      pageSize,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.searchTransactions;
    } catch (error) {
      throw error;
    }
  }
  async getCashbookTransactionDetail(cashTransactionId: string) {
    const query = GET_CASHBOOK_TRANSACTION_DETAIL;
    const variables = {
      partnerId: this.orgId,
      cashTransactionId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables)
      return response.getCashbookTransactionDetail
    } catch (error) {
      throw error
    }
  }
}
