import { gql } from "graphql-tag";

export const CHANGE_SERVICE_NAME = gql`
  mutation ChangeServiceName(
    $serviceId: String!
    $updateBy: String!
    $name: String!
  ) {
    changeServiceName(serviceId: $serviceId, updateBy: $updateBy, name: $name) {
      partnerId
      status
      ownerId
      startDate
      endDate
      actionRequest {
        id
        name
        uri
      }
    }
  }
`;

export const CREATE_USER_MAIL_HOSTING = gql`
  mutation CreateUserMailHosting(
    $serviceId: String!
    $username: String!
    $password: String!
    $fullName: String!
  ) {
    createUserMailHosting(
      serviceId: $serviceId
      username: $username
      password: $password
      fullName: $fullName
    )
  }
`;

export const DELETE_USER_MAIL_HOSTING = gql`
  mutation DeleteUserMailHosting($serviceId: String!, $username: String!) {
    deleteUserMailHosting(serviceId: $serviceId, username: $username)
  }
`;

export const UPDATE_DOMAIN_NAME = gql`
  mutation UpdateDomainName(
    $serviceId: String!
    $updateBy: String!
    $updateData: String!
  ) {
    updateDomainName(
      serviceId: $serviceId
      updateBy: $updateBy
      updateData: $updateData
    )
  }
`;

export const UPDATE_USER_NAME = gql`
  mutation UpdateUserName(
    $serviceId: String!
    $updateBy: String!
    $updateData: String!
  ) {
    updateUsername(
      serviceId: $serviceId
      updateBy: $updateBy
      updateData: $updateData
    )
  }
`;

export const UPDATE_PASSWORD = gql`
  mutation UpdatePassword(
    $serviceId: String!
    $updateBy: String!
    $updateData: String!
  ) {
    updatePassword(
      serviceId: $serviceId
      updateBy: $updateBy
      updateData: $updateData
    )
  }
`;

export const UPDATE_USER_PASSWORD = gql`
  mutation UpdateUserPassword(
    $serviceId: String!
    $username: String!
    $password: String!
  ) {
    updateUserPassword(
      serviceId: $serviceId
      username: $username
      password: $password
    )
  }
`;

export const UPDATE_DNS = gql`
  mutation UpdateDNS(
    $serviceId: String!
    $dns1: String
    $dns2: String
    $dns3: String
    $dns4: String
    $createBy: String
  ) {
    updateDNS(
      serviceId: $serviceId
      dns1: $dns1
      dns2: $dns2
      dns3: $dns3
      dns4: $dns4
      createBy: $createBy
    )
  }
`;
