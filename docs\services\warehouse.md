# Warehouse Service

The Warehouse Service manages inventory operations, stock levels, warehouse management, and product availability across multiple warehouse locations.

## Table of Contents

- [Overview](#overview)
- [Warehouse V1 Operations](#warehouse-v1-operations)
- [Warehouse V2 Operations](#warehouse-v2-operations)
- [Inventory Management](#inventory-management)
- [Stock Level Monitoring](#stock-level-monitoring)
- [Best Practices](#best-practices)

## Overview

The Warehouse Service provides comprehensive inventory and warehouse management functionality:

- **Inventory Tracking**: Monitor stock levels across multiple warehouses
- **Product Availability**: Check real-time product availability
- **Batch Operations**: Handle multiple inventory queries efficiently
- **Warehouse Management**: Manage multiple warehouse locations
- **Stock Alerts**: Monitor low stock and out-of-stock situations
- **Version Support**: Both V1 (single product) and V2 (batch) operations

## Warehouse V1 Operations

### Get Single Product Inventory

Check inventory for a single product in a specific warehouse:

```typescript
async function getProductInventory(productSku: string, warehouseId: string) {
  try {
    const inventory = await sdk.warehouse.getInventory(productSku, warehouseId);
    
    console.log('Product inventory:', {
      sku: inventory.sku,
      warehouseId: inventory.warehouseId,
      availableQuantity: inventory.availableQuantity,
      reservedQuantity: inventory.reservedQuantity,
      totalQuantity: inventory.totalQuantity,
      lastUpdated: inventory.lastUpdated
    });
    
    return inventory;
  } catch (error) {
    console.error('Failed to fetch product inventory:', error);
    throw error;
  }
}
```

### Inventory Data Structure (V1)

```typescript
interface InventoryV1 {
  sku: string;
  warehouseId: string;
  availableQuantity: number;
  reservedQuantity: number;
  totalQuantity: number;
  lastUpdated: Date;
  location?: string;
  binLocation?: string;
}
```

### Check Product Availability

```typescript
async function checkProductAvailability(productSku: string, warehouseId: string, requestedQuantity: number) {
  try {
    const inventory = await sdk.warehouse.getInventory(productSku, warehouseId);
    
    const isAvailable = inventory.availableQuantity >= requestedQuantity;
    
    console.log('Availability check:', {
      sku: productSku,
      requested: requestedQuantity,
      available: inventory.availableQuantity,
      isAvailable: isAvailable
    });
    
    return {
      isAvailable,
      availableQuantity: inventory.availableQuantity,
      shortfall: isAvailable ? 0 : requestedQuantity - inventory.availableQuantity
    };
  } catch (error) {
    console.error('Availability check failed:', error);
    throw error;
  }
}
```

## Warehouse V2 Operations

### Batch Inventory Check

Check inventory for multiple products simultaneously:

```typescript
async function getBatchInventory(warehouseId: string) {
  const products = [
    { productId: 'product-1', variantId: 'variant-1', sku: 'SKU001' },
    { productId: 'product-2', variantId: 'variant-2', sku: 'SKU002' },
    { productId: 'product-3', variantId: 'variant-3', sku: 'SKU003' }
  ];

  try {
    const inventoryResults = await sdk.warehouseV2.getInventory(warehouseId, products);
    
    console.log('Batch inventory results:');
    inventoryResults.forEach(result => {
      console.log(`${result.sku}: ${result.availableQuantity} available`);
    });
    
    return inventoryResults;
  } catch (error) {
    console.error('Batch inventory check failed:', error);
    throw error;
  }
}
```

### Product Query Structure (V2)

```typescript
interface ProductQuery {
  productId: string;
  variantId?: string;
  sku: string;
}

interface InventoryV2 {
  productId: string;
  variantId?: string;
  sku: string;
  warehouseId: string;
  availableQuantity: number;
  reservedQuantity: number;
  totalQuantity: number;
  incomingQuantity?: number;
  lastUpdated: Date;
  status: InventoryStatus;
}

type InventoryStatus = 
  | 'IN_STOCK'
  | 'LOW_STOCK'
  | 'OUT_OF_STOCK'
  | 'DISCONTINUED';
```

### Batch Availability Check

```typescript
async function checkBatchAvailability(
  warehouseId: string, 
  productRequests: Array<{ sku: string; quantity: number }>
) {
  try {
    // Get all product info for batch query
    const products = productRequests.map(req => ({
      productId: req.sku, // Assuming SKU as product ID for simplicity
      sku: req.sku
    }));

    const inventoryResults = await sdk.warehouseV2.getInventory(warehouseId, products);
    
    // Check availability for each requested product
    const availabilityResults = productRequests.map(request => {
      const inventory = inventoryResults.find(inv => inv.sku === request.sku);
      
      if (!inventory) {
        return {
          sku: request.sku,
          requested: request.quantity,
          available: 0,
          isAvailable: false,
          shortfall: request.quantity
        };
      }
      
      const isAvailable = inventory.availableQuantity >= request.quantity;
      
      return {
        sku: request.sku,
        requested: request.quantity,
        available: inventory.availableQuantity,
        isAvailable,
        shortfall: isAvailable ? 0 : request.quantity - inventory.availableQuantity
      };
    });
    
    console.log('Batch availability results:', availabilityResults);
    return availabilityResults;
  } catch (error) {
    console.error('Batch availability check failed:', error);
    throw error;
  }
}
```

## Inventory Management

### Inventory Manager Class

```typescript
class InventoryManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getInventoryAcrossWarehouses(sku: string, warehouseIds: string[]) {
    try {
      const inventoryPromises = warehouseIds.map(warehouseId =>
        this.sdk.warehouse.getInventory(sku, warehouseId)
          .catch(error => ({ warehouseId, error: error.message }))
      );

      const results = await Promise.all(inventoryPromises);
      
      const successfulResults = results.filter(result => !('error' in result));
      const failedResults = results.filter(result => 'error' in result);

      const totalInventory = {
        sku,
        totalAvailable: successfulResults.reduce((sum, inv) => sum + inv.availableQuantity, 0),
        totalReserved: successfulResults.reduce((sum, inv) => sum + inv.reservedQuantity, 0),
        warehouseBreakdown: successfulResults,
        errors: failedResults
      };

      return totalInventory;
    } catch (error) {
      console.error('Failed to get inventory across warehouses:', error);
      throw error;
    }
  }

  async findAvailableWarehouses(sku: string, requiredQuantity: number, warehouseIds: string[]) {
    try {
      const inventoryData = await this.getInventoryAcrossWarehouses(sku, warehouseIds);
      
      const availableWarehouses = inventoryData.warehouseBreakdown
        .filter(inv => inv.availableQuantity >= requiredQuantity)
        .sort((a, b) => b.availableQuantity - a.availableQuantity); // Sort by highest availability

      return {
        availableWarehouses,
        canFulfill: availableWarehouses.length > 0,
        totalAvailable: inventoryData.totalAvailable
      };
    } catch (error) {
      console.error('Failed to find available warehouses:', error);
      throw error;
    }
  }

  async getOptimalWarehouseAllocation(
    orderItems: Array<{ sku: string; quantity: number }>,
    warehouseIds: string[]
  ) {
    try {
      const allocations: Array<{
        sku: string;
        quantity: number;
        warehouseId: string;
        allocated: number;
      }> = [];

      for (const item of orderItems) {
        let remainingQuantity = item.quantity;
        
        const warehouseInventory = await this.getInventoryAcrossWarehouses(item.sku, warehouseIds);
        
        // Sort warehouses by available quantity (highest first)
        const sortedWarehouses = warehouseInventory.warehouseBreakdown
          .sort((a, b) => b.availableQuantity - a.availableQuantity);

        for (const warehouse of sortedWarehouses) {
          if (remainingQuantity <= 0) break;
          
          const allocatedQuantity = Math.min(remainingQuantity, warehouse.availableQuantity);
          
          if (allocatedQuantity > 0) {
            allocations.push({
              sku: item.sku,
              quantity: item.quantity,
              warehouseId: warehouse.warehouseId,
              allocated: allocatedQuantity
            });
            
            remainingQuantity -= allocatedQuantity;
          }
        }

        if (remainingQuantity > 0) {
          console.warn(`Cannot fully allocate ${item.sku}: ${remainingQuantity} units short`);
        }
      }

      return allocations;
    } catch (error) {
      console.error('Failed to get optimal warehouse allocation:', error);
      throw error;
    }
  }
}
```

## Stock Level Monitoring

### Stock Alert System

```typescript
class StockAlertSystem {
  private sdk: SDK;
  private lowStockThreshold: number;
  private criticalStockThreshold: number;

  constructor(sdk: SDK, lowStockThreshold = 10, criticalStockThreshold = 5) {
    this.sdk = sdk;
    this.lowStockThreshold = lowStockThreshold;
    this.criticalStockThreshold = criticalStockThreshold;
  }

  async checkStockLevels(warehouseId: string, productSkus: string[]) {
    try {
      const alerts: Array<{
        sku: string;
        currentStock: number;
        alertLevel: 'LOW' | 'CRITICAL' | 'OUT_OF_STOCK';
        message: string;
      }> = [];

      for (const sku of productSkus) {
        try {
          const inventory = await this.sdk.warehouse.getInventory(sku, warehouseId);
          
          if (inventory.availableQuantity === 0) {
            alerts.push({
              sku,
              currentStock: inventory.availableQuantity,
              alertLevel: 'OUT_OF_STOCK',
              message: 'Product is out of stock'
            });
          } else if (inventory.availableQuantity <= this.criticalStockThreshold) {
            alerts.push({
              sku,
              currentStock: inventory.availableQuantity,
              alertLevel: 'CRITICAL',
              message: `Critical stock level: ${inventory.availableQuantity} units remaining`
            });
          } else if (inventory.availableQuantity <= this.lowStockThreshold) {
            alerts.push({
              sku,
              currentStock: inventory.availableQuantity,
              alertLevel: 'LOW',
              message: `Low stock level: ${inventory.availableQuantity} units remaining`
            });
          }
        } catch (error) {
          console.error(`Failed to check stock for ${sku}:`, error);
        }
      }

      return alerts;
    } catch (error) {
      console.error('Stock level check failed:', error);
      throw error;
    }
  }

  async generateStockReport(warehouseId: string, productSkus: string[]) {
    try {
      const alerts = await this.checkStockLevels(warehouseId, productSkus);
      
      const report = {
        warehouseId,
        timestamp: new Date(),
        totalProducts: productSkus.length,
        outOfStock: alerts.filter(a => a.alertLevel === 'OUT_OF_STOCK').length,
        criticalStock: alerts.filter(a => a.alertLevel === 'CRITICAL').length,
        lowStock: alerts.filter(a => a.alertLevel === 'LOW').length,
        alerts
      };

      console.log('Stock Report:', {
        warehouse: report.warehouseId,
        outOfStock: report.outOfStock,
        criticalStock: report.criticalStock,
        lowStock: report.lowStock
      });

      return report;
    } catch (error) {
      console.error('Failed to generate stock report:', error);
      throw error;
    }
  }
}
```

### Inventory Analytics

```typescript
class InventoryAnalytics {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getInventoryTurnover(sku: string, warehouseId: string, periodDays: number = 30) {
    try {
      // This would typically require historical data
      // For now, we'll calculate based on current inventory
      const inventory = await this.sdk.warehouse.getInventory(sku, warehouseId);
      
      // Placeholder calculation - in real implementation, you'd need sales data
      const estimatedDailySales = 5; // This should come from sales analytics
      const turnoverRate = inventory.availableQuantity / (estimatedDailySales * periodDays);
      
      return {
        sku,
        warehouseId,
        currentStock: inventory.availableQuantity,
        estimatedDaysOfStock: inventory.availableQuantity / estimatedDailySales,
        turnoverRate,
        recommendation: this.getStockRecommendation(turnoverRate)
      };
    } catch (error) {
      console.error('Failed to calculate inventory turnover:', error);
      throw error;
    }
  }

  private getStockRecommendation(turnoverRate: number): string {
    if (turnoverRate < 0.1) {
      return 'REORDER_URGENT';
    } else if (turnoverRate < 0.3) {
      return 'REORDER_SOON';
    } else if (turnoverRate > 2) {
      return 'OVERSTOCK';
    } else {
      return 'OPTIMAL';
    }
  }

  async getWarehouseUtilization(warehouseId: string, productSkus: string[]) {
    try {
      const inventoryData = await Promise.allSettled(
        productSkus.map(sku => this.sdk.warehouse.getInventory(sku, warehouseId))
      );

      const successfulInventories = inventoryData
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as PromiseFulfilledResult<any>).value);

      const utilization = {
        warehouseId,
        totalProducts: productSkus.length,
        productsInStock: successfulInventories.filter(inv => inv.availableQuantity > 0).length,
        totalUnits: successfulInventories.reduce((sum, inv) => sum + inv.totalQuantity, 0),
        availableUnits: successfulInventories.reduce((sum, inv) => sum + inv.availableQuantity, 0),
        reservedUnits: successfulInventories.reduce((sum, inv) => sum + inv.reservedQuantity, 0),
        utilizationRate: 0
      };

      utilization.utilizationRate = utilization.totalUnits > 0 
        ? (utilization.totalUnits - utilization.availableUnits) / utilization.totalUnits 
        : 0;

      return utilization;
    } catch (error) {
      console.error('Failed to calculate warehouse utilization:', error);
      throw error;
    }
  }
}
```

## Best Practices

### 1. Error Handling and Resilience

```typescript
// ✅ Good: Handle inventory service errors gracefully
async function safeInventoryCheck(sku: string, warehouseId: string) {
  try {
    return await sdk.warehouse.getInventory(sku, warehouseId);
  } catch (error) {
    if (error.response?.status === 404) {
      console.log(`Product ${sku} not found in warehouse ${warehouseId}`);
      return {
        sku,
        warehouseId,
        availableQuantity: 0,
        reservedQuantity: 0,
        totalQuantity: 0,
        status: 'NOT_FOUND'
      };
    }
    throw error;
  }
}
```

### 2. Caching Strategy

```typescript
// ✅ Good: Cache inventory data to reduce API calls
class InventoryCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 60000; // 1 minute

  async getInventoryWithCache(sku: string, warehouseId: string): Promise<any> {
    const cacheKey = `${sku}_${warehouseId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const inventory = await sdk.warehouse.getInventory(sku, warehouseId);
    
    this.cache.set(cacheKey, {
      data: inventory,
      timestamp: Date.now()
    });

    return inventory;
  }

  clearCache() {
    this.cache.clear();
  }
}
```

### 3. Batch Processing

```typescript
// ✅ Good: Use V2 API for batch operations when possible
async function efficientInventoryCheck(warehouseId: string, skus: string[]) {
  // Use V2 API for batch processing
  const products = skus.map(sku => ({ productId: sku, sku }));
  
  try {
    return await sdk.warehouseV2.getInventory(warehouseId, products);
  } catch (error) {
    // Fallback to V1 API if V2 fails
    console.warn('V2 API failed, falling back to V1');
    
    const results = await Promise.allSettled(
      skus.map(sku => sdk.warehouse.getInventory(sku, warehouseId))
    );
    
    return results
      .filter(result => result.status === 'fulfilled')
      .map(result => (result as PromiseFulfilledResult<any>).value);
  }
}
```

### 4. Inventory Validation

```typescript
// ✅ Good: Validate inventory data
class InventoryValidator {
  static validateInventoryData(inventory: any): boolean {
    return (
      inventory &&
      typeof inventory.availableQuantity === 'number' &&
      typeof inventory.reservedQuantity === 'number' &&
      typeof inventory.totalQuantity === 'number' &&
      inventory.availableQuantity >= 0 &&
      inventory.reservedQuantity >= 0 &&
      inventory.totalQuantity >= 0 &&
      inventory.availableQuantity + inventory.reservedQuantity <= inventory.totalQuantity
    );
  }

  static validateStockRequest(sku: string, quantity: number): string[] {
    const errors: string[] = [];
    
    if (!sku || sku.trim().length === 0) {
      errors.push('SKU is required');
    }
    
    if (!quantity || quantity <= 0) {
      errors.push('Quantity must be greater than 0');
    }
    
    if (quantity > 10000) {
      errors.push('Quantity exceeds maximum allowed (10,000)');
    }
    
    return errors;
  }
}
```

---

**Next Steps:**
- [Computing Service](./computing.md) - Cloud computing services
- [Campaign Service](./campaign.md) - Marketing campaigns
- [Image Service](./image.md) - Image and media management
