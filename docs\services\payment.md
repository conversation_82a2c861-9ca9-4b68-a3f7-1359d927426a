# Payment Service

The Payment Service handles payment processing, invoice management, payment methods, and financial transactions for the e-commerce platform.

## Table of Contents

- [Overview](#overview)
- [Payment Methods](#payment-methods)
- [Payment Processing](#payment-processing)
- [Invoice Management](#invoice-management)
- [VAT Invoice Operations](#vat-invoice-operations)
- [QR Code Payments](#qr-code-payments)
- [Best Practices](#best-practices)

## Overview

The Payment Service provides comprehensive payment functionality:

- **Payment Methods**: Manage available payment options for customers
- **Payment Processing**: Handle payment transactions and order payments
- **Invoice Management**: Create, retrieve, and manage invoices
- **VAT Invoices**: Handle VAT invoice publication and management
- **QR Payments**: Generate QR codes for mobile payments
- **Transaction Tracking**: Monitor payment status and history

## Payment Methods

### Get Available Payment Methods

Retrieve payment methods available for the store:

```typescript
async function getPaymentMethods() {
  try {
    const methods = await sdk.payment.getPaymentMethodOfStoreChannel();
    
    console.log('Available payment methods:', methods.map(method => ({
      id: method.id,
      name: method.name,
      type: method.type,
      enabled: method.enabled
    })));
    
    return methods;
  } catch (error) {
    console.error('Failed to fetch payment methods:', error);
    throw error;
  }
}
```

### Payment Method Structure

```typescript
interface PaymentMethod {
  id: string;
  name: string;
  type: string;              // 'BANK_TRANSFER', 'CREDIT_CARD', 'E_WALLET', etc.
  enabled: boolean;
  description?: string;
  icon?: string;
  processingFee?: number;
  minAmount?: number;
  maxAmount?: number;
  supportedCurrencies?: string[];
}
```

### Filter Payment Methods

```typescript
class PaymentMethodManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getEnabledPaymentMethods(): Promise<PaymentMethod[]> {
    const allMethods = await this.sdk.payment.getPaymentMethodOfStoreChannel();
    return allMethods.filter(method => method.enabled);
  }

  async getPaymentMethodsByType(type: string): Promise<PaymentMethod[]> {
    const allMethods = await this.sdk.payment.getPaymentMethodOfStoreChannel();
    return allMethods.filter(method => method.type === type && method.enabled);
  }

  async getPaymentMethodsForAmount(amount: number): Promise<PaymentMethod[]> {
    const allMethods = await this.sdk.payment.getPaymentMethodOfStoreChannel();
    return allMethods.filter(method => {
      if (!method.enabled) return false;
      if (method.minAmount && amount < method.minAmount) return false;
      if (method.maxAmount && amount > method.maxAmount) return false;
      return true;
    });
  }
}
```

## Payment Processing

### Create Payment Order

Create a payment order for an existing order:

```typescript
async function createPaymentOrder(orderId: string, paymentMethodId: string) {
  const paymentData = {
    orderId: orderId,
    paymentMethodId: paymentMethodId,
    amount: 500000,
    currency: 'VND',
    description: `Payment for order ${orderId}`,
    returnUrl: 'https://your-app.com/payment/success',
    cancelUrl: 'https://your-app.com/payment/cancel'
  };

  try {
    const payment = await sdk.payment.createPaymentOrder(paymentData);
    
    console.log('Payment order created:', {
      paymentId: payment.id,
      status: payment.status,
      paymentUrl: payment.paymentUrl
    });
    
    return payment;
  } catch (error) {
    console.error('Payment order creation failed:', error);
    throw error;
  }
}
```

### Payment Order Data Structure

```typescript
interface CreatePaymentOrderRequest {
  orderId: string;
  paymentMethodId: string;
  amount: number;
  currency?: string;
  description?: string;
  returnUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, any>;
}

interface PaymentOrder {
  id: string;
  orderId: string;
  paymentMethodId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentUrl?: string;
  transactionId?: string;
  createdAt: Date;
  updatedAt: Date;
}

type PaymentStatus = 
  | 'PENDING'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'REFUNDED';
```

### Payment Status Tracking

```typescript
class PaymentTracker {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentStatus> {
    try {
      const payment = await this.getPaymentDetails(paymentId);
      return payment.status;
    } catch (error) {
      console.error('Failed to check payment status:', error);
      throw error;
    }
  }

  async waitForPaymentCompletion(
    paymentId: string, 
    maxWaitTime: number = 300000, // 5 minutes
    checkInterval: number = 5000   // 5 seconds
  ): Promise<PaymentOrder> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const payment = await this.getPaymentDetails(paymentId);
      
      if (payment.status === 'COMPLETED') {
        return payment;
      } else if (payment.status === 'FAILED' || payment.status === 'CANCELLED') {
        throw new Error(`Payment ${payment.status.toLowerCase()}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
    
    throw new Error('Payment timeout');
  }

  private async getPaymentDetails(paymentId: string): Promise<PaymentOrder> {
    // This would be implemented based on the actual API
    // For now, we'll assume there's a method to get payment details
    throw new Error('Method not implemented');
  }
}
```

## QR Code Payments

### Generate QR Payment

Generate QR code for mobile payments:

```typescript
async function generateQRPayment(orderId: string, paymentMethodId: string) {
  try {
    const qrData = await sdk.payment.genQrPayment(orderId, paymentMethodId);
    
    console.log('QR Payment generated:', {
      qrCode: qrData.qrCode,
      qrUrl: qrData.qrUrl,
      amount: qrData.amount,
      expiresAt: qrData.expiresAt
    });
    
    return qrData;
  } catch (error) {
    console.error('QR payment generation failed:', error);
    throw error;
  }
}
```

### QR Payment Data Structure

```typescript
interface QRPaymentData {
  qrCode: string;           // Base64 encoded QR code image
  qrUrl: string;            // QR code URL for scanning
  amount: number;           // Payment amount
  currency: string;         // Payment currency
  orderId: string;          // Associated order ID
  paymentId: string;        // Payment transaction ID
  expiresAt: Date;          // QR code expiration time
  bankInfo?: {
    bankCode: string;
    accountNumber: string;
    accountName: string;
  };
}
```

### QR Payment Helper

```typescript
class QRPaymentHelper {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async generateQRWithRetry(
    orderId: string, 
    paymentMethodId: string, 
    maxRetries: number = 3
  ): Promise<QRPaymentData> {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.sdk.payment.genQrPayment(orderId, paymentMethodId);
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
    throw new Error('Max retries exceeded');
  }

  async isQRCodeExpired(qrData: QRPaymentData): Promise<boolean> {
    return new Date() > new Date(qrData.expiresAt);
  }

  async refreshQRCode(orderId: string, paymentMethodId: string): Promise<QRPaymentData> {
    return await this.generateQRWithRetry(orderId, paymentMethodId);
  }
}
```

## Invoice Management

### Get Invoice Details

Retrieve detailed invoice information:

```typescript
async function getInvoiceDetails(invoiceId: string) {
  try {
    const invoice = await sdk.payment.getInvoiceDetail(invoiceId);
    
    console.log('Invoice details:', {
      id: invoice.id,
      number: invoice.number,
      amount: invoice.amount,
      status: invoice.status,
      issuedDate: invoice.issuedDate,
      dueDate: invoice.dueDate
    });
    
    return invoice;
  } catch (error) {
    console.error('Failed to fetch invoice details:', error);
    throw error;
  }
}
```

### Get Order Invoices

Retrieve all invoices for a specific order:

```typescript
async function getOrderInvoices(orderId: string) {
  try {
    const invoices = await sdk.payment.getInvoicesOfOrder(orderId);
    
    console.log(`Found ${invoices.length} invoices for order ${orderId}`);
    
    invoices.forEach(invoice => {
      console.log(`Invoice ${invoice.number}: ${invoice.amount} ${invoice.currency}`);
    });
    
    return invoices;
  } catch (error) {
    console.error('Failed to fetch order invoices:', error);
    throw error;
  }
}
```

### Invoice Data Structure

```typescript
interface Invoice {
  id: string;
  number: string;
  orderId: string;
  customerId: string;
  amount: number;
  currency: string;
  status: InvoiceStatus;
  issuedDate: Date;
  dueDate: Date;
  paidDate?: Date;
  items: InvoiceItem[];
  taxAmount?: number;
  discountAmount?: number;
  totalAmount: number;
}

interface InvoiceItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number;
}

type InvoiceStatus = 
  | 'DRAFT'
  | 'SENT'
  | 'PAID'
  | 'OVERDUE'
  | 'CANCELLED';
```

## VAT Invoice Operations

### Request VAT Invoice Publication

Request publication of a VAT invoice:

```typescript
async function requestVATInvoicePublication(invoiceId: string) {
  const vatRequest = {
    invoiceId: invoiceId,
    customerTaxCode: '0123456789',
    customerName: 'ABC Company Ltd',
    customerAddress: '123 Business Street',
    buyerEmail: '<EMAIL>',
    items: [
      {
        name: 'Product A',
        quantity: 2,
        unitPrice: 100000,
        totalPrice: 200000,
        taxRate: 10
      }
    ]
  };

  try {
    const result = await sdk.payment.requestPublishVatInvoice(vatRequest);
    
    console.log('VAT invoice publication requested:', {
      requestId: result.requestId,
      status: result.status
    });
    
    return result;
  } catch (error) {
    console.error('VAT invoice publication request failed:', error);
    throw error;
  }
}
```

### Request VAT Invoice Unpublication

Cancel a published VAT invoice:

```typescript
async function requestVATInvoiceUnpublication(invoiceId: string) {
  try {
    const result = await sdk.payment.requestUnpublishVatInvoice(invoiceId);
    
    console.log('VAT invoice unpublication requested:', result);
    return result;
  } catch (error) {
    console.error('VAT invoice unpublication request failed:', error);
    throw error;
  }
}
```

### View Published Invoice

View a published VAT invoice:

```typescript
async function viewPublishedInvoice(invoiceId: string) {
  try {
    const publishedInvoice = await sdk.payment.viewPublishedInvoice(invoiceId);
    
    console.log('Published invoice:', {
      invoiceNumber: publishedInvoice.invoiceNumber,
      publishDate: publishedInvoice.publishDate,
      downloadUrl: publishedInvoice.downloadUrl
    });
    
    return publishedInvoice;
  } catch (error) {
    console.error('Failed to view published invoice:', error);
    throw error;
  }
}
```

### VAT Invoice Data Structure

```typescript
interface VatInvoiceRequestDTO {
  invoiceId: string;
  customerTaxCode: string;
  customerName: string;
  customerAddress: string;
  buyerEmail: string;
  items: VatInvoiceItem[];
  notes?: string;
}

interface VatInvoiceItem {
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate: number;
  unit?: string;
}

interface PublishedInvoice {
  invoiceNumber: string;
  publishDate: Date;
  downloadUrl: string;
  status: string;
  customerInfo: {
    name: string;
    taxCode: string;
    address: string;
  };
}
```

## Best Practices

### 1. Payment Security

```typescript
// ✅ Good: Validate payment amounts and methods
class PaymentValidator {
  static validatePaymentAmount(amount: number): boolean {
    return amount > 0 && amount <= 1000000000; // Max 1 billion VND
  }

  static validatePaymentMethod(methodId: string, availableMethods: PaymentMethod[]): boolean {
    return availableMethods.some(method => method.id === methodId && method.enabled);
  }

  static sanitizePaymentData(data: any): any {
    // Remove sensitive information from logs
    const sanitized = { ...data };
    delete sanitized.cardNumber;
    delete sanitized.cvv;
    delete sanitized.pin;
    return sanitized;
  }
}
```

### 2. Error Handling

```typescript
// ✅ Good: Handle payment-specific errors
async function safePaymentOperation<T>(operation: () => Promise<T>): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (error.response?.status === 402) {
      throw new Error('Payment required - insufficient funds');
    } else if (error.response?.status === 409) {
      throw new Error('Payment already processed');
    } else if (error.response?.status === 422) {
      throw new Error('Invalid payment data');
    } else if (error.response?.status === 429) {
      throw new Error('Too many payment attempts - please try again later');
    }
    throw error;
  }
}
```

### 3. Payment Flow Management

```typescript
// ✅ Good: Implement complete payment flow
class PaymentFlowManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async processOrderPayment(orderId: string, paymentMethodId: string) {
    try {
      // 1. Validate payment method
      const methods = await this.sdk.payment.getPaymentMethodOfStoreChannel();
      const selectedMethod = methods.find(m => m.id === paymentMethodId);
      
      if (!selectedMethod || !selectedMethod.enabled) {
        throw new Error('Invalid payment method');
      }

      // 2. Create payment order
      const payment = await this.sdk.payment.createPaymentOrder({
        orderId,
        paymentMethodId,
        amount: 500000, // This should come from order total
        description: `Payment for order ${orderId}`
      });

      // 3. Generate QR code if needed
      let qrData = null;
      if (selectedMethod.type === 'QR_CODE') {
        qrData = await this.sdk.payment.genQrPayment(orderId, paymentMethodId);
      }

      return {
        payment,
        qrData,
        paymentMethod: selectedMethod
      };
    } catch (error) {
      console.error('Payment flow failed:', error);
      throw error;
    }
  }
}
```

### 4. Invoice Management

```typescript
// ✅ Good: Comprehensive invoice handling
class InvoiceManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getOrderInvoiceSummary(orderId: string) {
    try {
      const invoices = await this.sdk.payment.getInvoicesOfOrder(orderId);
      
      const summary = {
        totalInvoices: invoices.length,
        totalAmount: invoices.reduce((sum, inv) => sum + inv.amount, 0),
        paidInvoices: invoices.filter(inv => inv.status === 'PAID').length,
        pendingInvoices: invoices.filter(inv => inv.status === 'SENT').length,
        overdueInvoices: invoices.filter(inv => inv.status === 'OVERDUE').length
      };

      return { invoices, summary };
    } catch (error) {
      console.error('Failed to get invoice summary:', error);
      throw error;
    }
  }

  async processVATInvoice(invoiceId: string, customerInfo: any) {
    try {
      // Request VAT invoice publication
      const vatRequest = await this.sdk.payment.requestPublishVatInvoice({
        invoiceId,
        ...customerInfo
      });

      // Wait for processing (in real implementation, you'd use webhooks)
      await new Promise(resolve => setTimeout(resolve, 5000));

      // View published invoice
      const publishedInvoice = await this.sdk.payment.viewPublishedInvoice(invoiceId);

      return publishedInvoice;
    } catch (error) {
      console.error('VAT invoice processing failed:', error);
      throw error;
    }
  }
}
```

---

**Next Steps:**
- [CRM Service](./crm.md) - Customer relationship management
- [Warehouse Service](./warehouse.md) - Inventory management
- [Computing Service](./computing.md) - Cloud computing services
