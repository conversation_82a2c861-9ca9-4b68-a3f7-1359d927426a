import sdk from "../../setup";
describe("serviceManagerService", () => {
  let service: any;

  beforeAll(() => {
    service = sdk.service;
  });

  // test func
  // it("get servie by id ", async () => {
  //   const serviceId = "20.6085";
  //   try {
  //     const response = await service.getServiceById(serviceId);
  //     expect(response).toBeDefined();
  //   } catch (error) {
  //     throw error;
  //   }
  // });
  it("get service by owner id ", async () => {
    const ownerId = "20.52731";
    try {
      const response = await service.getServiceByOwnerId(ownerId);
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("create service ticket", async () => {
    const serviceId = "20.5760";
    const name = "service ticket";
    const createdBy = "admin";
    const description = "service ticket description";
    try {
      const response = await service.createServiceTicket(
        serviceId,
        name,
        createdBy,
        description
      );

      expect(response).toHaveProperty("serviceId", "20.5760");
    } catch (error) {
      throw error;
    }
  });

  it("get service actions", async () => {
    const serviceId = "20.5760";
    try {
      const response = await service.getServiceActions(serviceId);
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("update success action process status", async () => {
    const serviceActionId = "20.27592";
    const updatedBy = "admin";
    try {
      const response = await service.updateSuccessActionProcessStatus(
        serviceActionId,
        updatedBy
      );
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  // update fail action process status
  it("update fail action process status", async () => {
    const serviceActionId = "20.27592";
    const description = "action failed";
    const updatedBy = "admin";
    try {
      const response = await service.updateFailActionProcessStatus(
        serviceActionId,
        description,
        updatedBy
      );
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get service ticket", async () => {
    const serviceId = "20.5760";
    try {
      const response = await service.getServiceTicket(serviceId);
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("create service action", async () => {
    const serviceId = "20.5760";
    const actionType = "BOOKING";
    const createdBy = "admin";
    const attributes = {
      name: "ztbee",
      value: "123",
    }
    try {
      const response = await service.createServiceAction(
        serviceId,
        actionType, 
        attributes,
        createdBy
      );
      expect(response).toHaveProperty("actionType", actionType);
    } catch (error) {
      throw error;
    }
  });
  it("update attr value", async () => {
    const serviceId = "20.6205.2227";
    const attr = {
      name: "ztbee",
      value: "123",
    };
    const updatedBy = "+84372448071";
    try {
      const response = await service.updateAttrValue(
        serviceId,
        attr,
        updatedBy
      );
      console.log("🚀 ~ it ~ response:", response)
      // expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("delete attr value", async () => {
    const serviceId = "20.6205.2227";
    const attrName = "ztbee";
    const updatedBy = "+84372448071";
    try {
      const response = await service.deleteAttrValue(
        serviceId,
        attrName,
        updatedBy
      );
      console.log("🚀 ~ it ~ response:", response)
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  // async addActionAttribute(actionId: string, attributeName: string, attributeValue: string, createdBy: string) {
  //   const mutation = ADD_ACTION_ATTRIBUTE;
  //   const variables = {
  //     actionId,
  //     attributeName,
  //     attributeValue,
  //     createdBy,
  //   };
  //   try {
  //     const response = await this.graphqlMutation(mutation, variables);
  //     return response.addActionAttribute;
  //   } catch (error) {
  //     throw error;
  //   }
  // }
  it("add action attribute", async () => {
    const actionId = "20.27592";
    const attribute = {
      name: "ztbee",
      value: "123",
    };
    const createdBy = "+84372448071";
    try {
      const response = await service.addActionAttribute(
        actionId,
        attribute,
        createdBy
      );
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("get action attribute", async () => {
    const actionId = "20.27592";
    const attributeName = "ztbee";
    try {
      const response = await service.getActionAttribute(actionId, attributeName);
      expect(response).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
});
