export interface AddOpportunityRequest {
  name: string;
  description: string;
  parentId?: string | null;
  priorityName: string;
  referName: string;
  referPhone: string;
  referEmail: string;
  targetId: string;
  extSource: string;
  ownerId?: string;
  forms: [Map];
}
export interface Map {
  key: string;
  value: string;
}
export interface AddAttachmentRequestFull {
  name: string;
  description: string;
  parentId?: string | null;
  priorityName: string;
  referName: string;
  referPhone: string;
  referEmail: string;
  targetId: string;
  extSource: string;
  ownerId?: string;
}
export interface Opportunity {
  goal: string;
  campaignId: string;
  valueReal: number;
  valueExpect: number;
  successRate: number;
  referName?: string | null;
  referPhone?: string | null;
  referEmail?: string | null;
  id: string;
  createdBy: string;
  ownerId?: string | null;
  workEffortTypeId: string;
  partyId?: string | null;
  name: string;
  description: string;
  parentId: string | null;
  status: string;
  stmId: string;
  createdStamp: string;
  updatedStamp: string;
  endDateExpect?: string | null;
  priorityName: string;
  targetId?: string | null;
  targetType?: string | null;
  targetUrl?: string | null;
  extSource?: string | null;
  connectorId?: string | null;
  processResult?: string | null;
}

export interface GetOpportunityRequest {
  id?: string;
  pageSize: number;
  pageNumber: number;
  isPagination: boolean;
  workEffortTypeId: string;
  sort: {
    key: string;
    asc: boolean;
  };
}

export interface GetOpportunity {
  total: number;
  data: OpjectOpportunity[];
}

export interface OpjectOpportunity {
  goal: string;
  campaignId: string;
  valueReal: number;
  valueExpect: number;
  successRate: number;
  referName: string;
  referPhone: string;
  referEmail: string;
  id: string;
  createdBy: string;
  ownerId: string;
  workEffortTypeId: string;
  partyId: string;
  name: string;
  description: string;
  parentId: string;
  status: number;
  stmId: string;
  createdStamp: string;
  updatedStamp: string;
  endDateExpect: string;
  priorityName: string;
  targetId: string;
  targetType: string;
  targetUrl: string;
  extSource: string;
  connectorId: string;
  processResult: number;
}

export interface getListWorkEffortType {
  id: string;
  name: string;
  group: string;
  createdStamp: string;
  updatedStamp: string;
  updatedBy: string;
  createdBy: string;
  partyId: string;
  actionLinkId: string;
  partyGroupIds: string[];
  description: string;
  workFlow: workFlow;
}

export interface ListToDo {
  workEffortId: string;
  toDoList: toDoList;
}

export interface toDoList {
  listAttachment: listAttachment;
  isDone: boolean;
  id: string;
  workEffortTypeId: string;
  workEffortType: workEffortType;
  partyId: string;
  name: string;
  description: string;
  parentId: string;
  parentType: string;
  status: number;
  createdStamp: string;
  updatedStamp: string;
  source: string;
  mode: string;
  connectorId: string;
  actionLink: actionLink;
  partyGroupIds: string;
  tagIds: string;
  processResult: number;
}

export interface actionLink {
  name: string;
  uri: string;
  type: string;
  partyId: string;
  fromCollection: string;
  toCollection: string;
  group: string;
  params: string;
  id: string;
  createdStamp: string;
  updatedStamp: string;
  updatedBy: string;
  createdBy: string;
}

export interface workEffortType {
  id: string;
  name: string;
  group: string;
  createdStamp: string;
  updatedStamp: string;
  updatedBy: string;
  createdBy: string;
  partyId: string;
  actionLinkId: string;
  partyGroupIds: string;
  description: string;
  workFlow: workFlow;
}

export interface workFlow {
  stages: stages;
}

export interface stages {
  id: string;
  name: string;
  mode: string;
  workEffortTypeId: string;
}

export interface listAttachment {
  id: string;
  createdStamp: string;
  createdBy: string;
  updatedBy: string;
  updatedStamp: string;
  partyId: string;
  path: string;
  srcId: string;
  srcName: string;
  srcPath: string;
  srcConfigPathId: string;
  name: string;
  fileType: string;
  type: string;
  status: string;
  referId: string[];
}

export interface updateWorkEffortDescription {
  id: string;
  createdStamp: string;
  updatedStamp: string;
  createdBy: string;
  updatedBy: string;
  name: string;
  partyId: string;
  targetId: string;
  targetType: string;
  targetUrl: string;
  description: string;
  status: number;
  parentId: string;
  workEffortTypeId: string;
  stmId: string;
  workflowId: string;
  endDateExpect: string;
  endDateActual: string;
  startDateActual: string;
  startDateExpect: string;
  source: string;
  ownerId: string;
  priorityName: string;
  priorityValue: number;
  extSource: string;
  extSourceTopicId: string;
  extSourceSocialAppId: string;
  extSourceSupportChannelType: string;
  extSourceSocialChannelType: string;
  extSourceSocialAppName: string;
  extSourceTopicUrl: string;
  connectorId: string;
  mode: string;
  partyGroupIds: [string];
  tagIds: [string];
  processResult: number;
}

export interface getTicketRequest {
  estimateTime: string;
  keyword: string;
  id: string;
  ids: [string];
  ignoreIds: string;
  partyGroupId: string;
  name: string;
  workEffortTypeId: string;
  createdBy: string;
  partyId: string;
  accountableId: string;
  parentId: string;
  status: number;
  statuses: [number];
  processResult: number;
  ownerId: string;
  priorityName: Priority;
  targetId: string;
  targetType: string;
  fromDate: Date;
  toDate: Date;
  isPagination: boolean;
  sort: sort;
  pageNumber: number;
  pageSize: number;
  extSource: string;
  connectorId: string;
}

export interface sort {
  key: string;
  asc: boolean;
}

export interface Priority {
  HIGH: "HIGH";
  MEDIUM: "MEDIUM";
  LOW: "LOW";
}

export interface AddTicketRequest {
  name: string;
  description: string;
  workEffortTypeId: string;
  workEffortTypeAction: string;
  ownerId: string;
  parentId: string;
  priorityName: Priority;
  accountableId: string;
  targetId: string;
  targetType: string;
  targetUrl: string;
  extSource: string;
  connectorId: string;
  startDateExpect: string;
}

export interface AddAttachmentRequest {
  srcId: string;
  srcName: string;
  srcPath: string;
  srcConfigPathId?: string;
  name?: string;
  type?: string;
  fileType?: string;
}

export interface GetCommentRequest {
  referId: string;
  type: CommentType;
}

export interface CommentType {
  GLOBAL: "GLOBAL";
  INTERNAL: "INTERNAL";
}
export interface BaseSort {
  key: string;
  asc: boolean;
}
