import { gql } from "graphql-tag";

export const CREATE_SERVICE = gql`
  mutation CreateService($partnerId: String!, $createModel: ServiceRequest!) {
    createCompany(partnerId: $partnerId, createModel: $createModel) {
      createdStamp: DateCustom
      updatedStamp: DateCustom
      updatedBy: String
      createdBy: String
      partnerId: String
      serviceName: String
      serviceId: String
      type: String
      typeName: String
      orderId: String
      orderLineItemId: String
      orderItemTermId: String
      status: String
      ownerId: String
      ownerName: String
      ownerEmail: String
      ownerPhone: String
      saleName: String
      salePartyId: String
      startDate: DateCustom
      endDate: DateCustom
      version: String
      location: String
      description: String
      username: String
      password: String
      urlPrivate: String
      urlPublic: String
      serviceType: Int
      actionResult: String
    }
  }
`;

export const UPDATE_SERVICE = gql`
  mutation UpdateService(
    $partnerId: String!
    $serviceId: String!
    $updateModel: ServiceRequest!
    $updatedBy: String!
  ) {
    updateService(
      partnerId: $partnerId
      serviceId: $serviceId
      updateModel: $updateModel
      updatedBy: $updateBy
    ) {
      createdStamp: DateCustom
      updatedStamp: DateCustom
      updatedBy: String
      createdBy: String
      partnerId: String
      serviceName: String
      serviceId: String
      type: String
      typeName: String
      orderId: String
      orderLineItemId: String
      orderItemTermId: String
      status: String
      ownerId: String
      ownerName: String
      ownerEmail: String
      ownerPhone: String
      saleName: String
      salePartyId: String
      startDate: DateCustom
      endDate: DateCustom
      version: String
      location: String
      description: String
      username: String
      password: String
      urlPrivate: String
      urlPublic: String
      serviceType: Int
      actionResult: String
    }
  }
`;

export const DELETE_SERVICE = gql`
  mutation UpdateService(
    $partnerId: String!
    $serviceId: String!
    $deletedBy: String!
  ) {
    updateService(
      partnerId: $partnerId
      serviceId: $serviceId
      deletedBy: $deletedBy
    )
  }
`;

// export const UPDATE_ATTR_VALUE = gql`
//   mutation UpdateAttrValue(
//     $partnerId: String!
//     $serviceId: String!
//     $attrName: String!
//     $attrValue: String!
//     $updatedBy: String!
//   ) {
//     updateAttrValue(
//       partnerId: $partnerId
//       serviceId: $serviceId
//       attrName: $attrName
//       attrValue: $attrValue
//       updatedBy: $updatedBy
//     )
//   }
// `;
export const DELETE_ATTR_VALUE = gql`
  mutation DeleteAttrValue(
    $partnerId: String!
    $serviceId: String!
    $attrName: String!
    $updatedBy: String!
  ) {
    deleteAttrValue(
      partnerId: $partnerId
      serviceId: $serviceId
      attrName: $attrName
      updatedBy: $updatedBy
    ){
      createdStamp
        updatedStamp
        updatedBy
        createdBy
        partnerId
        serviceName
        serviceId
        type
        typeName
        orderId
        orderLineItemId
        orderItemTermId
        status
        ownerId
        ownerName
        ownerEmail
        ownerPhone
        saleName
        salePartyId
        startDate
        endDate
        version
        location
        description
        username
        password
        urlPrivate
        urlPublic
        serviceType
        actionResult
        attrs  
    }
  }
`;

export const UPDATE_SUCCESS_ACTION_PROCESS_STATUS = gql`
  mutation UpdateSuccessActionProcessStatus(
    $serviceActionId: String!
    $updatedBy: String!
  ) {
    updateSuccessActionProcessStatus(
      serviceActionId: $serviceActionId
      updatedBy: $updatedBy
    ) {
      id
      serviceId
      actionType
      description
      status
      processStatus
      createdBy
      createdStamp
      updatedBy
      updatedStamp
    }
  }
`;
export const UPDATE_FAIL_ACTION_PROCESS_STATUS = gql`
  mutation UpdateFailActionProcessStatus(
    $serviceActionId: String!
    $decription: String
    $updatedBy: String!
  ) {
    updateFailActionProcessStatus(
      serviceActionId: $serviceActionId
      description: $decription
      updatedBy: $updatedBy
    ) {
      id
      serviceId
      actionType
      description
      status
      processStatus
      createdBy
      createdStamp
      updatedBy
      updatedStamp
    }
  }
`;

export const CREATE_SERVICE_TICKET = gql`
  mutation CreateServiceTicket(
    $serviceId: String!
    $name: String!
    $createdBy: String!
    $description: String
  ) {
    createServiceTicket(
      serviceId: $serviceId
      name: $name
      createdBy: $createdBy
      description: $description
    ) {
      id
      serviceId
      status
      processStatus
      name
      description
      createdBy
      createdStamp
      updatedBy
      updatedStamp
    }
  }
`;

//   mutation CreateServiceAction {
//     createServiceAction(serviceId: null, actionType: null, createdBy: null) {
//         id
//         serviceId
//         actionType
//         description
//         status
//         processStatus
//         createdBy
//         createdStamp
//         updatedBy
//         updatedStamp
//     }
// } 
export const CREATE_SERVICE_ACTION = gql`
  mutation CreateServiceAction(
    $serviceId: String!
    $actionType: String!
    $attributes : JSON
    $createdBy: String!
  ) {
    createServiceAction(
      serviceId: $serviceId
      actionType: $actionType
      attributes: $attributes
      createdBy: $createdBy
    ) {
      id
      serviceId
      actionType
      description
      status
      processStatus
      createdBy
      createdStamp
      updatedBy
      updatedStamp
      attributes
    }
  }
`;
export const UPDATE_ATTR_VALUE = gql`
  mutation UpdateAttrValue(
    $partnerId: String!
    $serviceId: String!
    $attr: JSON!
    $updatedBy: String!
  ) {
    updateAttrValue(
      partnerId: $partnerId
      serviceId: $serviceId
      attr: $attr
      updatedBy: $updatedBy
    ){
       createdStamp
        updatedStamp
        updatedBy
        createdBy
        partnerId
        serviceName
        serviceId
        type
        typeName
        orderId
        orderLineItemId
        orderItemTermId
        status
        ownerId
        ownerName
        ownerEmail
        ownerPhone
        saleName
        salePartyId
        startDate
        endDate
        version
        location
        description
        username
        password
        urlPrivate
        urlPublic
        serviceType
        actionResult
        attrs
      }

  }
`;

export const ADD_ACTION_ATTRIBUTE = gql`
  mutation AddActionAttribute(
    $actionId: String!
    $attribute : JSON!
    $createdBy: String!
  ) {
    addActionAttribute(
      actionId: $actionId
      attribute: $attribute
      createdBy: $createdBy
    ){
       id
        serviceId
        actionType
        description
        status
        processStatus
        createdBy
        createdStamp
        updatedBy
        updatedStamp
        attributes
        }
  }
`;
