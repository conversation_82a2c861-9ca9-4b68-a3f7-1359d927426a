# Authentication Service

The Authentication Service handles user authentication, registration, password management, and social login integrations.

## Table of Contents

- [Overview](#overview)
- [Basic Authentication](#basic-authentication)
- [User Registration](#user-registration)
- [Password Management](#password-management)
- [Social Authentication](#social-authentication)
- [Token Management](#token-management)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## Overview

The Authentication Service provides comprehensive user authentication capabilities:

- **Username/Password Authentication**: Traditional login system
- **Social Login**: Google, Facebook, Zalo integration
- **Password Reset**: SMS-based password recovery
- **User Registration**: Account creation with validation
- **Token Management**: JWT token handling

## Basic Authentication

### Login

Authenticate users with username and password:

```typescript
async function loginUser(username: string, password: string) {
  try {
    const loginResponse = await sdk.auth.login({
      username,
      password
    });
    
    // Store token for subsequent requests
    sdk.setToken(loginResponse.accessToken);
    
    console.log('Login successful:', {
      userId: loginResponse.partyId,
      name: loginResponse.fullName,
      email: loginResponse.email,
      orgId: loginResponse.orgId
    });
    
    return loginResponse;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
}
```

### Login Response Structure

```typescript
interface LoginResponse {
  partyId: string;           // User ID
  orgId: string;             // Organization ID
  fullName: string;          // User's full name
  email: string;             // User's email
  phone: string;             // User's phone number
  address: string;           // User's address
  identityNumber: string;    // Identity/ID number
  gender: string;            // User's gender
  birthDate: string;         // Birth date
  avatarUrl: string;         // Profile picture URL
  accessToken: string;       // JWT access token
  username: string;          // Username
  orgPermissionsMap: Record<string, any>;  // Organization permissions
  orgPositionsMap: Record<string, any>;    // Organization positions
  orgRolesMap: Record<string, any>;        // Organization roles
}
```

### Get User Details

Retrieve user information using an access token:

```typescript
async function getUserDetails(accessToken: string) {
  try {
    const userDetails = await sdk.auth.getUserDetail(accessToken);
    console.log('User details:', userDetails);
    return userDetails;
  } catch (error) {
    console.error('Failed to get user details:', error);
    throw error;
  }
}
```

## User Registration

### Basic Registration

Register a new user account:

```typescript
async function registerUser(userData: {
  username: string;
  fullName: string;
  password: string;
  userIP: string;
}) {
  try {
    await sdk.auth.register({
      username: userData.username,
      fullName: userData.fullName,
      password: userData.password,
      userIP: userData.userIP
    });
    
    console.log('Registration successful');
    
    // Optionally login the user immediately
    return await loginUser(userData.username, userData.password);
  } catch (error) {
    console.error('Registration failed:', error);
    throw error;
  }
}
```

### Registration with Validation

```typescript
function validateRegistrationData(data: {
  username: string;
  fullName: string;
  password: string;
}) {
  const errors: string[] = [];
  
  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.username)) {
    errors.push('Invalid email format');
  }
  
  // Password validation
  if (data.password.length < 8) {
    errors.push('Password must be at least 8 characters');
  }
  
  // Name validation
  if (data.fullName.trim().length < 2) {
    errors.push('Full name must be at least 2 characters');
  }
  
  return errors;
}

async function registerWithValidation(userData: {
  username: string;
  fullName: string;
  password: string;
  userIP: string;
}) {
  // Validate input
  const validationErrors = validateRegistrationData(userData);
  if (validationErrors.length > 0) {
    throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
  }
  
  return await registerUser(userData);
}
```

## Password Management

### Send SMS Verification Code

Send a verification code for password reset:

```typescript
async function sendPasswordResetCode(username: string) {
  try {
    const response = await sdk.auth.sendSmsVerifyCode(username);
    console.log('Verification code sent:', response);
    return response;
  } catch (error) {
    console.error('Failed to send verification code:', error);
    throw error;
  }
}
```

### Verify SMS Code

Verify the SMS code received by the user:

```typescript
async function verifyResetCode(username: string, code: string) {
  try {
    const verifyResponse = await sdk.auth.verifyCode({
      username,
      code
    });
    
    console.log('Code verified successfully');
    return verifyResponse;
  } catch (error) {
    console.error('Code verification failed:', error);
    throw error;
  }
}
```

### Reset Password

Reset user password after code verification:

```typescript
async function resetPassword(
  username: string,
  newPassword: string,
  accessToken: string
) {
  try {
    await sdk.auth.resetPassword({
      username,
      newPassword,
      accessToken
    });
    
    console.log('Password reset successful');
  } catch (error) {
    console.error('Password reset failed:', error);
    throw error;
  }
}
```

### Complete Password Reset Flow

```typescript
async function completePasswordReset(username: string, newPassword: string) {
  try {
    // Step 1: Send verification code
    console.log('Sending verification code...');
    await sendPasswordResetCode(username);
    
    // Step 2: Get code from user (in real app, this would be user input)
    const code = prompt('Enter the verification code:');
    if (!code) throw new Error('Verification code required');
    
    // Step 3: Verify code
    console.log('Verifying code...');
    const verifyResponse = await verifyResetCode(username, code);
    
    // Step 4: Reset password
    console.log('Resetting password...');
    await resetPassword(username, newPassword, verifyResponse.accessToken);
    
    console.log('Password reset completed successfully!');
  } catch (error) {
    console.error('Password reset flow failed:', error);
    throw error;
  }
}
```

## Social Authentication

### Google OAuth

Initiate Google OAuth login:

```typescript
async function loginWithGoogle(redirectUrl: string) {
  try {
    const result = await sdk.auth.loginGoogle(redirectUrl);
    console.log('Google login URL:', result);
    
    // In a web app, redirect user to this URL
    // window.location.href = result.authUrl;
    
    return result;
  } catch (error) {
    console.error('Google login failed:', error);
    throw error;
  }
}
```

### Facebook OAuth

Initiate Facebook OAuth login:

```typescript
async function loginWithFacebook(redirectUrl: string) {
  try {
    const result = await sdk.auth.loginFacebook(redirectUrl);
    console.log('Facebook login URL:', result);
    
    // In a web app, redirect user to this URL
    // window.location.href = result.authUrl;
    
    return result;
  } catch (error) {
    console.error('Facebook login failed:', error);
    throw error;
  }
}
```

### Zalo OAuth

Initiate Zalo OAuth login:

```typescript
async function loginWithZalo(redirectUrl: string) {
  try {
    const result = await sdk.auth.loginZalo(redirectUrl);
    console.log('Zalo login URL:', result);
    
    // In a web app, redirect user to this URL
    // window.location.href = result.authUrl;
    
    return result;
  } catch (error) {
    console.error('Zalo login failed:', error);
    throw error;
  }
}
```

### Social Login Component (React Example)

```typescript
import React from 'react';
import { useLongvanSDK } from '../hooks/useLongvanSDK';

export function SocialLoginButtons() {
  const sdk = useLongvanSDK();
  const redirectUrl = `${window.location.origin}/auth/callback`;

  const handleGoogleLogin = async () => {
    try {
      const result = await sdk.auth.loginGoogle(redirectUrl);
      window.location.href = result.authUrl;
    } catch (error) {
      console.error('Google login failed:', error);
    }
  };

  const handleFacebookLogin = async () => {
    try {
      const result = await sdk.auth.loginFacebook(redirectUrl);
      window.location.href = result.authUrl;
    } catch (error) {
      console.error('Facebook login failed:', error);
    }
  };

  const handleZaloLogin = async () => {
    try {
      const result = await sdk.auth.loginZalo(redirectUrl);
      window.location.href = result.authUrl;
    } catch (error) {
      console.error('Zalo login failed:', error);
    }
  };

  return (
    <div className="social-login-buttons">
      <button onClick={handleGoogleLogin} className="google-btn">
        Login with Google
      </button>
      <button onClick={handleFacebookLogin} className="facebook-btn">
        Login with Facebook
      </button>
      <button onClick={handleZaloLogin} className="zalo-btn">
        Login with Zalo
      </button>
    </div>
  );
}
```

## Token Management

### Setting and Managing Tokens

```typescript
class AuthManager {
  private sdk: SDK;
  private currentToken: string | null = null;

  constructor(sdk: SDK) {
    this.sdk = sdk;
    this.loadTokenFromStorage();
  }

  async login(username: string, password: string) {
    const response = await this.sdk.auth.login({ username, password });
    this.setToken(response.accessToken);
    return response;
  }

  setToken(token: string) {
    this.currentToken = token;
    this.sdk.setToken(token);
    this.saveTokenToStorage(token);
  }

  clearToken() {
    this.currentToken = null;
    this.sdk.setToken('');
    this.removeTokenFromStorage();
  }

  isAuthenticated(): boolean {
    return !!this.currentToken;
  }

  private saveTokenToStorage(token: string) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('longvan_access_token', token);
    }
  }

  private loadTokenFromStorage() {
    if (typeof localStorage !== 'undefined') {
      const token = localStorage.getItem('longvan_access_token');
      if (token) {
        this.setToken(token);
      }
    }
  }

  private removeTokenFromStorage() {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('longvan_access_token');
    }
  }
}
```

## Error Handling

### Common Authentication Errors

```typescript
function handleAuthError(error: any) {
  if (error.response) {
    const status = error.response.status;
    const message = error.response.data?.message || error.message;
    
    switch (status) {
      case 401:
        console.error('Invalid credentials:', message);
        // Redirect to login page
        break;
      case 403:
        console.error('Access forbidden:', message);
        // Show access denied message
        break;
      case 429:
        console.error('Too many attempts:', message);
        // Show rate limit message
        break;
      default:
        console.error('Authentication error:', message);
    }
  } else {
    console.error('Network error:', error.message);
  }
}
```

## Best Practices

### 1. Secure Token Storage

```typescript
// ✅ Good: Use secure storage
class SecureTokenStorage {
  private static readonly TOKEN_KEY = 'longvan_access_token';

  static saveToken(token: string) {
    // In production, consider using secure storage libraries
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(this.TOKEN_KEY, token);
    }
  }

  static getToken(): string | null {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem(this.TOKEN_KEY);
    }
    return null;
  }

  static clearToken() {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(this.TOKEN_KEY);
    }
  }
}
```

### 2. Input Validation

```typescript
// ✅ Good: Always validate user input
function validateLoginInput(username: string, password: string) {
  if (!username || !password) {
    throw new Error('Username and password are required');
  }
  
  if (username.length < 3) {
    throw new Error('Username must be at least 3 characters');
  }
  
  if (password.length < 8) {
    throw new Error('Password must be at least 8 characters');
  }
}
```

### 3. Error Recovery

```typescript
// ✅ Good: Implement retry logic for network errors
async function loginWithRetry(username: string, password: string, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await sdk.auth.login({ username, password });
    } catch (error) {
      if (i === maxRetries - 1 || error.response?.status === 401) {
        throw error; // Don't retry on authentication errors
      }
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

---

**Next Steps:**
- [Product Service](./product.md) - Product catalog management
- [Order Service](./order.md) - Order processing
- [User Service](./user.md) - Customer management
