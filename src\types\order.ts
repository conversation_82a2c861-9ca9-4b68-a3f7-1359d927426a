export interface LineItem {
  quantity: number;
  parent_id?: string;
  product_id: string;
  input_price?: number;
  discount_amount?: number;
}

export interface DiscountCampaign {
  type_discount: string;
  discount_amount: 0;
  campaign_id: string;
  campaign_action_id: string;
  campaign_action_type: string;
}
export interface CancelOrder {
  reason: "CUSTOMER";
  updatedBy: string;
  note: string;
  orderType: "SALES";
}
export interface MemberDiscount {
  type: string;
  amount: 0;
  campaignId: string;
  campaignActionId: string;
  campaignActionType: string;
}
export interface CampaignPromotion {
  campaign_id: string;
  campaign_action_id: string;
  campaign_action_type: string;
}
export interface OrderQuery {
  order_id?: string;
  status?: [number];
  status_ignore?: [number];
  sub_status?: string;
  ffm_status?: string;
  sub_type?: string;
  payment_method?: string;
  keyword?: string;
  customer_multi_value?: string;
  product_multi_value?: string;
  customer_id?: string;
  created_by?: string;
  date_create_from?: number;
  date_create_to?: number;
  date_update_from?: number;
  date_update_to?: number;
  employee_assign?: string;
  currentPage?: number;
  maxResult?: number;
  source?: string;
}
export interface ShippingAddress {
  name: string,
  phone: string,
  address:string,
  province_code: string,
  district_code: string,
  ward_code: string,
  address_default?: boolean
}

export interface CustomerInfo {
  id: string;
  email: string;
  name: string;
  phone: string;
}