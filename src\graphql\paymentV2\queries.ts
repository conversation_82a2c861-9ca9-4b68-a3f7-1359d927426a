import { gql } from "graphql-tag";

export const PAYMENTS_BY_ORDERS = gql`
  query PaymentsByOrders($orderIds: [String!]!) {
    paymentsByOrders(orderIds: $orderIds) {
      methodCode
      methodTypeCode
      gatewayConfigId
      partnerCode
      totalAmount
      fee
      discountAmount
      payDate
      payType
      transactionDate
      orderId
      paymentId
      invoiceId
      orderInfo
      orderType
      locale
      merchantIp
      extraData
      storeID
      bankCode
      appUser
      phone
      email
      address
      statusCode
      statusDescription
      transactionId
      createBy
      methodDescription
      methodMapping
      baseMethodCode
      confirmPaidBy
      confirmPaidDate
      qrCode
      payUrl
      paymentNote
    }
  }
`;
export const PAYMENT_METHODS = gql`
  query PaymentMethods($partnerCode: String) {
    paymentMethods(partnerCode: $partnerCode) {
      id
      code
      name
      description
      image
    }
  }
`;
export const GET_PAYMENT_METHOD_TYPES = gql`
  query GetPaymentMethodTypes($partnerId: String!, $storeId: String!) {
    getPaymentMethodTypes(partnerId: $partnerId, storeId: $storeId) {
      code
      name
      description
      image
    }
  }
`;
export const PAYMENT_INFO = gql`
  query PaymentInfo($paymentId: String!) {
    paymentInfo(paymentId: $paymentId) {
      id
      payType
      payDate
      dueDate
      completePayment
      paymentTerm
      methodCode
      methodTypeCode
      gatewayConfigId
      partnerCode
      totalAmount
      remain
      currCode
      invoiceId
      orderId
      orderInfo
      orderGroup
      orderType
      returnUrl
      ipnUrl
      merchantIp
      extraData
      storeID
      bankCode
      appUser
      phone
      email
      address
      signature
      createBy
      transactionId
      baseMethodCode
      retry
      postToOrder
      editAble
      approved
      statusCode
      paymentGroup
      userId
      type
      attributes
      items {
        id
        partnerCode
        paymentId
        type
        orderId
        invoiceId
        orderGroup
        orderType
        orderInfo
        totalAmount
      }
    }
  }
`;
export const PAYMENT_STATUS = gql`
  query PaymentStatus($paymentId: String!) {
    paymentStatus(paymentId: $paymentId) {
      methodCode
      methodTypeCode
      gatewayConfigId
      partnerCode
      totalAmount
      fee
      discountAmount
      payDate
      payType
      transactionDate
      paymentId
      invoiceId
      orderInfo
      orderType
      locale
      merchantIp
      extraData
      storeID
      bankCode
      appUser
      phone
      email
      address
      statusCode
      statusDescription
      transactionId
      createBy
      methodDescription
      methodMapping
      baseMethodCode
      confirmPaidBy
      confirmPaidDate
      qrCode
      payUrl
      attributes
    }
  }
`;
export const GW_CONFIG_DETAIL = gql`
  query GwConfigDetail($configId: String!) {
    gwConfigDetail(configId: $configId) {
      id
      name
      methodCode
      partnerCode
      subMethodCode
      gwPartnerCode
      gwPartnerName
      gwSubChannel
      gwMethodVersion
      accessKey
      secretKey
      description
      activated
    }
  }
`;
