import { gql } from "graphql-tag";
export const CANCEL_PAYMENT = gql`
  mutation CancelPayment($paymentId: String!, $reason: String!) {
    cancelPayment(paymentId: $paymentId, reason: $reason)
  }
`;
export const CONFIRM_TO_GATEWAY = gql`
  mutation ConfirmToGateWay(
    $paymentId: String!
    $methodCode: String
    $returnUrl: String
  ) {
    confirmToGateway(
      paymentId: $paymentId
      methodCode: $methodCode
      returnUrl: $returnUrl
    ) {
      code
      message
      data
      qrCodeUrl
      deeplink
      deeplinkMiniApp
      paymentId
      gwConfigId
    }
  }
`;

export const CONFIRM_PAYMENT_SUCCESS_MANUAL = gql`
  mutation ConfirmPaymentSuccessManual(
    $paymentId: String!
    $transactionNo: String!
    $note: String
    $confirmBy: String!
  ) {
    confirmPaymentSuccessManual(
      paymentId: $paymentId
      transactionNo: $transactionNo
      note: $note
      confirmBy: $confirmBy
    ) {
      code
      message
      data
      qrCodeUrl
      deeplink
      deeplinkMiniApp
      paymentId
    }
  }
`;
