import { Service } from "../serviceSDK";
export class DeepLinkVietQrService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async getAndroidBank(){
    const endpoint = `/android-app-deeplinks`
    const method: "GET" = "GET"
    try {
        const response = await this.restApiCallWithToken(endpoint, method)
        return response
    } catch (error) {
        throw error
    }
  }
  async getIosBank(){
    const endpoint = `/ios-app-deeplinks`
    const method: "GET" = "GET"
    try {
        const response = await this.restApiCallWithToken(endpoint, method)
        return response
    } catch (error) {
        throw error
    }
  }
}
