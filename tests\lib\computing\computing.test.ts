import sdk from "../../setup";

describe("ComputingService", () => {
    let computingService: any;
  beforeAll(() => {
    computingService = sdk.computing;
  });

  beforeAll(() => {
    computingService = sdk.computing;
  });
  it("check computingService", async () => {
    // const warehouseId = "1704257227604416";
    // const listProduct = [{ productId: "121094", variantId: "121096", sku: "" }];
    try {
      const response = await computingService.computingDetail(
        "20.1952"
      );
      console.log("🚀 ~ it ~ response:", response)
      expect(response).toBeDefined();
    //   return response;
    } catch (error) {
      throw error;
    }
  });
});
