import { gql } from "graphql-tag";

export const COMPUTING_DETAIL = gql`
  query ComputingDetail($computingId: String!) {
    computingDetail(computingId: $computingId) {
      id
      name
      username
      password
      state
      os
      ips
      province
      console
      existPortNat
    }
  }
`;

export const PORTNATS = gql`
  query PortNats($computingId: String!) {
    portNats(computingId: $computingId) {
      id
      action
      protocol
      originalAddress
      originalPort
      translatedAddress
      translatedPort
      description
      status
    }
  }
`;

export const SNAP_SHOTS = gql`
  query Snapshots($computingId: String!) {
    snapshots(computingId: $computingId) {
      id
      computingId
      snapshotId
      name
      status
      createdStamp
      endDate
    }
  }
`;
