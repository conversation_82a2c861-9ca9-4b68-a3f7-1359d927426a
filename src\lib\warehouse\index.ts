import { ListProduct } from "../../types/warehouse";
import { Service } from "../serviceSDK";

export class WarehouseService extends Service {
  /**
   * Constructs a new OrderService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }
  /**
   * get product inventory
   * @param warehouseId - the id of the warehouse
   * @param listProduct - list product
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async getInventory(sku: string,warehouseId: string) {
    const endpoint = `/${sku}/${warehouseId}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoToken(
        endpoint,
        method,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
