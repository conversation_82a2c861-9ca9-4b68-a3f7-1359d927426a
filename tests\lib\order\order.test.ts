// tests/lib/order/order.test.ts

import sdk from "../../setup";

describe("OrderService", () => {
  let orderService: any;

  beforeAll(() => {
    orderService = sdk.order;
  });
  it("add line item", async () => {
    const orderId = "20.413900";
    const lineItems = [
      {
        quantity: 1,
        parent_id: "120701",
        product_id: "120701",
        input_price: 220000,
        discount_amount: 0,
      },
    ];
    try {
      const order = await orderService.addOrderLineItems(orderId, lineItems);
      console.log(order);
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("update amount product in order", async () => {
    const orderId = "20.413900";
    const orderItemId = "1239499767027503104";
    const quantityNew = 1;
    try {
      const order = await orderService.updateQuantityProductInOrder(
        orderId,
        orderItemId,
        quantityNew
      );
      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("update price in order ", async () => {
    const orderId = "20.413900";
    const orderItemId = "1239499767027503104";
    const priceNew = 30000;
    try {
      const order = await orderService.updatePriceInOrder(
        orderId,
        orderItemId,
        priceNew
      );
      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("update discount price in order ", async () => {
    const orderId = "20.413900";
    const orderItemId = "1239499767027503104";
    const data = {
      type_discount: "",
      discount_amount: 0,
      campaign_id: "",
      campaign_action_id: "",
      campaign_action_type: "",
    };
    try {
      const order = await orderService.updateDiscountPriceInOrder(
        orderId,
        orderItemId,
        data
      );
      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("create order integration test", async () => {
    const data = {
      orderType: "POS_SALE",
      customer_id: "20.51269",
      line_items: [
        {
          id: "121122",
          productName: "",
          quantity: 1,
          typeDiscount: "MONEY",
          parent_id: "121121",
          product_id: "121122",
          supplier_id: "",
          input_price: 220000,
          discount_amount: 0,
        },
      ],
    };

    const platform = "WEB";
    const create_draft = false;
    const order = await orderService.createOrder(data, platform, create_draft);
    expect(order).toHaveProperty("status", 1);
  });

  // it("update quantity product in order", async () => {
  //   const orderId = "20.413900";
  //   const orderItemId = "20.52526";
  //   const quantity = 10;
  //   const order = await orderService.updateQuantityProductInOrder(
  //     orderId,
  //     orderItemId,
  //     quantity
  //   );
  //   expect(order).toHaveProperty("status", 0);
  // });

  it("update sale employee", async () => {
    const orderId = "20.413900";
    const saleId = "20.51269";
    const updateBy = "20.52526";

    try {
      const order = await orderService.updateSaleEmployee(
        orderId,
        saleId,
        updateBy
      );
      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("update VAT", async () => {
    const orderId = "20.413900";
    const vatFee = 5;
    const vatType = "VALUE_GOODS";
    try {
      const order = await orderService.updateStatusSellOrder(
        orderId,
        vatFee,
        vatType
      );
    } catch (error) {}
  });
  it("update status sell order", async () => {
    const orderId = "20.413900";
    const status = "OPEN";
    try {
      const order = await orderService.updateStatusSellOrder(orderId, status);
      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  //   expect(response).toBeDefined();
  // });
  //   test("addVoucher", async () => {
  //     // Replace with your actual values
  //     const orderId = "testOrderId";
  //     const voucherCode = "testVoucherCode";

  //     const response = await orderService.addVoucher(orderId, voucherCode);

  //     // Add your assertions here
  //     expect(response).toBeDefined();
  //   });

  //   test("removeVoucher", async () => {
  //     // Replace with your actual values
  //     const orderId = "testOrderId";
  //     const voucherCode = "testVoucherCode";

  //     const response = await orderService.removeVoucher(orderId, voucherCode);

  //     // Add your assertions here
  //     expect(response).toBeDefined();
  //   });

  //   test("updateVAT", async () => {
  //     // Replace with your actual values
  //     const orderId = "testOrderId";
  //     const vatFee = 10;
  //     const vatType = "VALUE_GOODS";

  //     const response = await orderService.updateVAT(orderId, vatFee, vatType);

  //     // Add your assertions here
  //     expect(response).toBeDefined();
  //   });
  it("update type of order", async () => {
    const orderId = "20.413900";
    const orderType = "POS_SALES";
    const updateBy = "20.51269";
    try {
      const order = await orderService.updateOrderType(
        orderId,
        orderType,
        updateBy
      );
      console.log(order);

      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("complete order ", async () => {
    const orderId = ["20.413900"];
    try {
      const order = await orderService.completeOrder(orderId);
      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("cancel order", async () => {
    const orderId = "20.413900";
    const data = {
      reason: "CUSTOMER",
      updatedBy: "20.51269",
      note: "huy don",
      orderType: "SALES",
    };
    try {
      const order = await orderService.updateCancelOrder(orderId, data);
      console.log(order);

      expect(order).toHaveProperty("status", 1);
    } catch (error) {
      throw error;
    }
  });
  it("update status of order", async () => {
    const orderId = "20.413900";
    const statusNew = "SHIPMENT";
    const updateBy = "20.52526";
    try {
      const order = await orderService.updateStatusReturnOrder(
        orderId,
        statusNew,
        updateBy
      );
      console.log(order);

      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("update discount ", async () => {
    const orderId = "";
    const requestdata = {
      type: "",
      amount: "",
      campaignId: "",
      campaignActionId: "",
      campaignActionType: "",
    };
    try {
      const order = await orderService.updateDiscount(orderId, requestdata);
      expect(order).toHaveProperty("status", 0);
    } catch (error) {
      throw error;
    }
  });
  const data = {
    time: 1713491841256,
  };
  // test GET
  it("get list shipping service ", async () => {
    const shippingCarrierId = "20.10008";
    try {
      const order = await orderService.getListShippingService(
        shippingCarrierId
      );
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get info return order ", async () => {
    const orderId = "20.413900";
    try {
      const order = await orderService.getInfoReturnOrder(orderId);

      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get info sell order ", async () => {
    const orderId = "20.413900";
    try {
      const order = await orderService.getInfoSellOrder(orderId);
      console.log(order);

      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get list type order", async () => {
    try {
      const order = await orderService.getListTypeOrder();
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get list shipping carrier", async () => {
    try {
      const order = await orderService.getListShippingCarrier();
      console.log(order);

      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get list sell order", async () => {
    const order_id = "20.413900";

    const data = {
      status: "SHIPMENT",
    };
    try {
      const order = await orderService.getListSellOrder(order_id, data);
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("get list sell order satus ", async () => {
    try {
      const order = await orderService.getListSaleOrderStatus();
      console.log(order);

      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get list return order ", async () => {
    const orderId = "20.413900";
    const data = {
      orderId: "20.413900",
    };
    try {
      const order = await orderService.getListReturnOrder(data);
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });

  it("get list return order status", async () => {
    try {
      const order = await orderService.getListReturnOrderStatus();
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  //post
  it("print order pdf", async () => {
    const orderId = "20.413900";
    // const paymentMethod = "COD";
    try {
      const order = await orderService.printOrderPdf(orderId);
      expect(order).toBeUndefined();
    } catch (error) {
      throw error;
    }
  });

  it("print order html ", async () => {
    const orderId = "20.413900";
    const paymentMethod = "COD";
    try {
      const order = await orderService.printOrderHtml(orderId, paymentMethod);
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  // lack of data(false)
  it("create return order ", async () => {
    const data = {
      saleOrderId: "string",
      saleId: "string",
      returnOrderId: "string",
      customerId: "string",
      customerName: "string",
      customerPhone: "string",
      customerEmail: "string",
      pickupAddress: {
        id: "string",
        createdStamp: "2024-05-10T07:49:48.926Z",
        updatedStamp: "2024-05-10T07:49:48.926Z",
        updatedBy: "string",
        createdBy: "string",
        type: "string",
        ownerPartyId: "string",
        taxCode: "string",
        represent: "string",
        address: "string",
        company: "string",
        invoiceReceiveEmail1: "string",
        invoiceReceiveEmail2: "string",
        toCollection: "string",
        status: "string",
        name: "string",
        phone: "string",
        provinceGeoId: "string",
        districtGeoId: "string",
        wardGeoId: "string",
        provinceName: "string",
        districtName: "string",
        wardName: "string",
        primary: true,
      },
      carrierId: "string",
      shippingServiceId: "string",
      storeId: "string",
      warehouseId: "string",
      platform: "string",
      note: "string",
      items: [
        {
          productId: "string",
          sku: "string",
          title: "string",
          quantity: 0,
          unitPrice: {
            amount: 0,
            currencyCode: "USD",
          },
          totalPrice: {
            amount: 0,
            currencyCode: "USD",
          },
        },
      ],
      orderDate: "2024-05-10T07:49:48.926Z",
      orderType: "SALES",
      subTotalPrice: {
        amount: 0,
        currencyCode: "USD",
      },
      totalPrice: {
        amount: 0,
        currencyCode: "USD",
      },
      feeReturn: {
        amount: 0,
        currencyCode: "USD",
      },
      feeShipping: {
        amount: 0,
        currencyCode: "USD",
      },
    };
    try {
      const order = await orderService.createOrderReturn(data);
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  it("calculate value order ", async () => {
    const data = {
      items: [
        {
          productId: "string",
          quantity: 0,
          price: {
            amount: 0,
            currencyCode: "USD",
          },
          discount: {
            type: "string",
            value: 0,
          },
        },
      ],
      inputDiscount: {
        type: "string",
        value: 0,
      },
      memberDiscount: {
        type: "string",
        value: 0,
      },
      voucherCodes: ["string"],
      shippingFee: {
        amount: 0,
        currencyCode: "USD",
      },
      vat: 0,
    };
    try {
      const order = await orderService.calculateOrder(data);
      expect(order).toBeDefined();
    } catch (error) {
      throw error;
    }
  });
  // delete
  it("delete draft order", async () => {
    const orderId = "20.413900";
    const updatedBy = "20.51269";

    try {
      const order = await orderService.removeDraftOrder(orderId, updatedBy);
      expect(order).toHaveProperty("status", 0);
    } catch (error) {
      throw error;
    }
  });
  it("cancel product in order ", async () => {
    const orderId = "20.413900";
    const orderItemId = "1239508222454960128";
    const reason = "like";
    try {
      const order = await orderService.cancelProductInOrder(
        orderId,
        orderItemId,
        reason
      );
      expect(order).toHaveProperty("status", 0);
    } catch (error) {
      throw error;
    }
  });
  it("delete product in order ", async () => {
    const orderId = "20.413900";
    const orderItemId = "1239501383143165952";
    const reason = "likea";
    try {
      const order = await orderService.removeProductInOrder(
        orderId,
        orderItemId,
        reason
      );
      expect(order).toHaveProperty("status", 0);
    } catch (error) {
      throw error;
    }
  });
});
