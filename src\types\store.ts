// src/types/store.ts

/**
 * Store Data Transfer Object
 * Represents a store/channel in the system
 */
export interface StoreDTO {
  /** Unique identifier for the store */
  id: string;

  /** Timestamp when the store was created */
  createdStamp: string | null;

  /** Display name of the store */
  name: string;

  /** Type of store (pos, ecommerce, etc.) */
  type: 'pos' | 'ecommerce' | 'marketplace' | 'wholesale' | string;

  /** Whether the store is enabled/active */
  enable: boolean;

  /** Party/Organization ID that owns this store */
  partyId: string;

  /** Array of warehouse IDs associated with this store */
  warehouses: string[];

  /** Default warehouse ID for this store */
  warehouseIdDefault: string;

  /** Whether future orders are allowed */
  enableOrderAbleFuture: boolean;

  /** Whether negative quantity orders are allowed */
  enableOrderNegativeQuantity: boolean;

  /** E-commerce specific store name */
  storeEcommerceName: string | null;

  /** Array of shipping company IDs */
  shippingCompanies: string[];

  /** Primary shipping company ID */
  shippingCompanyIdPrimary: string;

  /** Primary customer ID */
  customerIdPrimary: string;

  /** Primary payment method ID */
  paymentMethodIdPrimary: string;

  /** Whether custom product pricing is enabled */
  enableCustomProductPrice: boolean;

  /** Whether partial payments are enabled */
  enablePaymentPartial: boolean;

  /** Array of allowed partial payment percentages */
  paymentPartialPercent: number[];

  /** Link to the product store (for e-commerce) */
  productStoreLink: string | null;

  /** GraphQL typename */
  __typename?: string;
}

/**
 * Request interface for creating a new store
 */
export interface CreateStoreRequest {
  /** Partner/Organization ID */
  partnerId: string;

  /** Name for the new store */
  storeName: string;

  /** Optional store type */
  type?: 'pos' | 'ecommerce' | 'marketplace' | 'wholesale';

  /** Optional configuration */
  config?: Partial<StoreConfiguration>;
}

/**
 * Store configuration options
 */
export interface StoreConfiguration {
  /** Whether the store should be enabled by default */
  enable: boolean;

  /** Warehouse IDs to associate with the store */
  warehouses: string[];

  /** Default warehouse ID */
  warehouseIdDefault: string;

  /** Whether to enable future orders */
  enableOrderAbleFuture: boolean;

  /** Whether to enable negative quantity orders */
  enableOrderNegativeQuantity: boolean;

  /** E-commerce store name */
  storeEcommerceName: string;

  /** Shipping company IDs */
  shippingCompanies: string[];

  /** Primary shipping company ID */
  shippingCompanyIdPrimary: string;

  /** Primary customer ID */
  customerIdPrimary: string;

  /** Primary payment method ID */
  paymentMethodIdPrimary: string;

  /** Enable custom product pricing */
  enableCustomProductPrice: boolean;

  /** Enable partial payments */
  enablePaymentPartial: boolean;

  /** Partial payment percentages */
  paymentPartialPercent: number[];

  /** Product store link */
  productStoreLink: string;
}

/**
 * Request interface for getting store channels by employee ID
 */
export interface GetStoreChannelRequest {
  /** Employee/Sale ID */
  saleId: string;

  /** Optional filter by store type */
  type?: 'pos' | 'ecommerce' | 'marketplace' | 'wholesale';

  /** Optional filter by enabled status */
  enabledOnly?: boolean;
}

/**
 * Response interface for store channel queries
 */
export interface StoreChannelResponse {
  /** Array of accessible stores */
  stores: StoreDTO[];

  /** Total count of stores */
  totalCount: number;

  /** Whether user has admin permissions (can see all stores) */
  hasAdminAccess: boolean;
}

/**
 * Store filter options for querying stores
 */
export interface StoreFilterOptions {
  /** Filter by store type */
  type?: 'pos' | 'ecommerce' | 'marketplace' | 'wholesale' | string;

  /** Filter by enabled status */
  enabled?: boolean;

  /** Filter by party/organization ID */
  partyId?: string;

  /** Search by store name */
  searchName?: string;

  /** Pagination - current page */
  currentPage?: number;

  /** Pagination - max results per page */
  maxResult?: number;
}

/**
 * Store statistics interface
 */
export interface StoreStats {
  /** Total number of stores */
  totalStores: number;

  /** Number of active stores */
  activeStores: number;

  /** Number of inactive stores */
  inactiveStores: number;

  /** Breakdown by store type */
  byType: {
    pos: number;
    ecommerce: number;
    marketplace: number;
    wholesale: number;
    other: number;
  };
}

/**
 * Store service method return types
 */
export interface StoreServiceMethods {
  createStore: (partnerId: string, storeName: string) => Promise<StoreDTO>;
  getStoreChannelByEmpId: (saleId: string) => Promise<StoreDTO[]>;
}

// Export all types for external use
export type {
  StoreDTO as Store,
};
