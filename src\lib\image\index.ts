import { Service } from "../serviceSDK";

export class ImageService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }
  async getImageProduct(imageId: string) {
    const endpoint = `/${this.orgId}/${imageId}/images-url`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async getImageProducts(productCode: string) {
    try {
      const response = await fetch(
        `https://product-service.dev.longvan.vn/product-service/v1/products/${this.orgId}/${productCode}/images-url`
      );
      if (!response.ok) {
        throw new Error("Failed to get image products");
      }
      return response.json();
    } catch (error) {
      console.error("Error getting image products:", error);
    }
  }
}
