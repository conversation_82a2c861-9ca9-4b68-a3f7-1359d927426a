export interface createCompanyRequest {
  name: string;
  phone: string;
  address: string;
  email: string;
}

export interface CreateCompany {
  id: string;
  name: string;
  address: string;
  gender: string;
  identityNumber: string;
  birthDate: Date;
  email: string;
  phone: string;
  createdStamp: Date;
  createdBy: string;
  memberLevel: string;
}

export interface updateCustomerRequest {
  name: string;
  phone: string;
  address: string;
  birthDate: Date;
  email: string;
  gender: string;
}

export interface GetPersonByPartyIds {
  status: string;
  partyId: string;
  fullName: string;
  phone: string;
  address: string;
  gender: string;
  birthDate: Date;
  email: string;
  personalTitle: string;
  imageUrl: string;
  identityNumber: string;
  addressModel: addressModel;
  id: string;
}

export interface addressModel {
  id: string;
  addressInfo: string;
  provinceGeoId: string;
  districtGeoId: string;
  wardGeoId: string;
  provinceName: string;
  districtName: string;
  wardName: string;
  isDefault: boolean;
}

export interface createCustomerRequest {
  name: string;
  phone: string;
  email: string;
  birthDate: Date;
}

export interface dataCustomerRequest {
  name: string;
  phone: string;
  email: string;
  address: string;
  birthDate: Date;
}
export interface customerQuery {
  keyword: string;
  type: string;
  startCreatedDate?: number;
  endCreatedDate?: number;
  memberLevel?: string;
  currentPage?: number | 1;
  pageSize: number | 20;
  followSocialAppId?: string
}
export interface employeesQuery {
  keyword: string;
  positionShortName: string;
}
export interface customer {
  id: string;
  name: string;
  address: string;
  gender: string;
  identityNumber: string;
  birthDate: Date;
  email: string;
  phone: string;
  createdStamp: Date;
  createdBy: string;
  memberLevel: string;
}
export interface searchCustomersResponse {
  content: customer[];
}
