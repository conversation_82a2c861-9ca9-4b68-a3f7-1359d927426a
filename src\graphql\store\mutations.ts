import { gql } from "graphql-tag";

export const CREATE_STORE_MUTATION = gql`
  mutation CreateStore($partnerId: String, $storeName: String) {
    createStore(partnerId: $partnerId, storeName: $storeName) {
      id
      createdStamp
      name
      type
      enable
      partyId
      warehouses
      warehouseIdDefault
      enableOrderAbleFuture
      enableOrderNegativeQuantity
      storeEcommerceName
      shippingCompanies
      shippingCompanyIdPrimary
      customerIdPrimary
      paymentMethodIdPrimary
      enableCustomProductPrice
      enablePaymentPartial
      paymentPartialPercent
      productStoreLink
    }
  }
`;
