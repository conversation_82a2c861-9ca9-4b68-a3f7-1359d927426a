import { Service } from "../serviceSDK";
export class ComhubService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async sendZns(dataTemplate: any, appId: string, messageType: string) {
    const endpoint = `/web-hook/message/v2/send/social/ZNS/app/${appId}/message-type/${messageType}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        dataTemplate
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  async sendMessage(
    OAId: string,
    content: string,
    contentType: string,
    senderPartyId: string,
    receivePartyIds: [string]
  ) {
    const endpoint = `/web-hook/message/send/social/app/${OAId}`;
    const method: "POST" = "POST";
    const dataRequet = {
      content,
      senderPartyId,
      receivePartyIds,
    };
    try {
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        dataRequet
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
