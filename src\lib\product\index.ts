import { Service } from "../serviceSDK";
import {
  GET_POLICY,
  GET_PRODUCT_BY_ID_QUERY,
  GET_PRODUCT_BY_SLUG_QUERY,
  GET_PRODUCT_OPTION,
  GET_SIMPLE_PRODUCTS_QUERY,
  GET_STORES,
  GET_DETAIL_STORES,
  GET_PRODUCT_IMAGE,
  GET_CATEGORIES_QUERY,
  GET_PRODUCT,
  GET_UNITS,
  GET_PRODUCT_VARIANT_BY_ID,
} from "../../graphql/product/queries";
import {
  UPDATE_PRODUCT_TITLE,
  UPDATE_PRICE,
  UPDATE_PRICE_PROMOTION,
  UPDATE_CATEGORY,
  UPDATE_SHORT_DESCRIPTION,
  UPDATE_UNIT,
  CLEAR_ALL_CACHES,
} from "../../graphql/product/mutations";
import { Product } from "../../types/product";

/**
 * Service class for managing product-related operations.
 */
export class ProductService extends Service {
  /**
   * Constructs a new ProductService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */

  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  // ...

  /**
   * Retrieves a product by its ID.
   * @param productId - The ID of the product.
   * @returns A promise that resolves to the product.
   * @throws If an error occurs while fetching the product.
   */
  async getProductById(productId: string) {
    const query = GET_PRODUCT_BY_ID_QUERY;
    const variables = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      productId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getProductById;
    } catch (error) {
      console.log(`Error fetching product by ID: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves a product by its slug.
   * @param slug - The slug of the product.
   * @returns A promise that resolves to the product.
   * @throws If an error occurs while fetching the product.
   */
  async getProductBySlug(slug: string): Promise<Product> {
    const query = GET_PRODUCT_BY_SLUG_QUERY;

    const variables = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      handle: slug,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getProductByHandle;
    } catch (error) {
      console.log(`Error fetching product by slug: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves simple products based on the provided variables.
   * @param variables - The variables for the query.
   * @returns A promise that resolves to the simple products.
   * @throws If an error occurs while fetching the simple products.
   */
  async getSimpleProducts(variables: any): Promise<Product[] | null> {
    const query = GET_SIMPLE_PRODUCTS_QUERY;
    const variablesHandle = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      ...variables,
    };

    try {
      const response = await this.graphqlQuery(query, variablesHandle);
      return response.getSimpleProducts;
    } catch (error) {
      console.log(`Error fetching simple products: ${error}`);
      throw error;
    }
  }

  async getProductOption(productId: string) {
    const query = GET_PRODUCT_OPTION;
    const variablesHandle = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      productId,
    };

    try {
      const response = await this.graphqlQuery(query, variablesHandle);
      return response.getProductOption;
    } catch (error) {
      console.log(`Error fetching simple products: ${error}`);
      throw error;
    }
  }

  async getPolicy(groupId: string) {
    const query = GET_POLICY;
    const variablesHandle = {
      groupId,
    };

    try {
      const response = await this.graphqlQuery(query, variablesHandle);
      return response.getPolicy;
    } catch (error) {
      console.log(`Error fetching getPolicy: ${error}`);
      throw error;
    }
  }

  // ...
  async getStores(type: string) {
    const query = GET_STORES;
    const variablesHandle = {
      partnerId: this.orgId,
      type,
    };
    try {
      const response = await this.graphqlQuery(query, variablesHandle);
      return response.getStores;
    } catch (error) {
      console.log("error in getStores");
      throw error;
    }
  }
  async getDetailStores() {
    const query = GET_DETAIL_STORES;
    const variablesHandle = {
      partnerId: this.orgId,
      storeId: this.storeId,
    };
    try {
      const response = await this.graphqlQuery(query, variablesHandle);
      return response.getDetailStores;
    } catch (error) {
      throw error;
    }
  }
  //
  async getDetailStoresV2(storeId: string) {
    const query = GET_DETAIL_STORES;
    const variablesHandle = {
      partnerId: this.orgId,
      storeId,
    };
    try {
      const response = await this.graphqlQuery(query, variablesHandle);
      return response.getDetailStores;
    } catch (error) {
      throw error;
    }
  }
  //
  getProductImage = async (productId: string) => {
    const query = GET_PRODUCT_IMAGE;
    const variables = {
      partnerId: this.orgId,
      productId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getProductImage;
    } catch (error) {
      throw error;
    }
  };
  async getCategory(typeBuild: string, level: number) {
    const query = GET_CATEGORIES_QUERY;
    const variables = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      typeBuild,
      level,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getCategories;
    } catch (error) {
      throw error;
    }
  }
  async getProduct(dataQuery: any) {
    const query = GET_PRODUCT;
    const variables = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      keyword: dataQuery.keyword,
      category: dataQuery.category,
      currentPage: dataQuery.currentPage,
      maxResult: dataQuery.maxResult,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getProducts;
    } catch (error) {
      throw error;
    }
  }
  async updateProductTitle(
    productId: string,
    title: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_PRODUCT_TITLE;
    const variables = {
      productId: productId,
      title: title,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updateProductTitle;
    } catch (error) {
      throw error;
    }
  }
  async updatePrice(productId: string, price: string, updatedBy: string) {
    const mutation = UPDATE_PRICE;
    const variables = {
      productId: productId,
      storeId: this.storeId,
      price: price,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updatePrice;
    } catch (error) {
      throw error;
    }
  }
  async updatePricePromotion(
    productId: string,
    price: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_PRICE_PROMOTION;
    const variables = {
      productId: productId,
      storeId: this.storeId,
      price: price,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updatePricePromotion;
    } catch (error) {
      throw error;
    }
  }
  async updateCategory(
    productId: string,
    categoryId: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_CATEGORY;
    const variables = {
      productId: productId,
      categoryId: categoryId,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updateCategory;
    } catch (error) {
      throw error;
    }
  }
  async updateShortDescription(
    productId: string,
    shortDescription: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_SHORT_DESCRIPTION;
    const variables = {
      productId: productId,
      shortDescription: shortDescription,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updateShortDescription;
    } catch (error) {
      throw error;
    }
  }
  async updateUnit(productId: string, unitId: string, updatedBy: string) {
    const mutation = UPDATE_UNIT;
    const variables = {
      productId: productId,
      unitId: unitId,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updateUnit;
    } catch (error) {
      throw error;
    }
  }
  async getUnits() {
    const query = GET_UNITS;
    const variables = {
      partnerId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getUnits;
    } catch (error) {
      throw error;
    }
  }
  async clearAllCaches() {
    const mutaion = CLEAR_ALL_CACHES;
    const variables = {};
    try {
      const response = await this.graphqlMutation(mutaion, variables);
      return response.clearAllCaches;
    } catch (error) {
      throw error;
    }
  }
  async getProductVariantById(variantId: string) {
    const query = GET_PRODUCT_VARIANT_BY_ID;
    const variables = {
      partnerId: this.orgId,
      storeChannel: this.storeId,
      variantId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getProductVariantById;
    } catch (error) {
      throw error;
    }
  }
}
