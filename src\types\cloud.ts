export interface CreateUserMailHosting {
  serviceId: string;
  username: string;
  password: string;
  fullName: string;
}

export interface UpdateUserPassword {
  serviceId: string;
  username: string;
  password: string;
}

export interface DeleteUserPassword {
  serviceId: string;
  username: string;
}

export interface UpdateInforMationService {
  serviceId: string;
  useUpdate: string;
  valueUpdate: string;
}

export interface Filter {
  serviceId: string;
  type: string;
  status: ServiceStatus;
  salePartyId: string;
  offset: number;
  maxResult: number;
  ownerId: string;
  commonSearch: string;
}

export interface ServiceStatus {
  CREATED: "CREATED";
  UPGRADED: "UPGRADED";
  PROCESSING: "PROCESSING";
  COMPLETED: "COMPLETED";
  ERROR: "ERROR";
  SUSPEND: "SUSPEND";
  CANCELLED: "CANCELLED";
  CLOSED: "CLOSED";
}
