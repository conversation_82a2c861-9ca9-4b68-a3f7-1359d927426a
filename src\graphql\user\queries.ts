import { gql } from "graphql-tag";
export const GET_PERSON_BY_IDS_QUERY = gql`
  query GetPersonByIds($partyIds: [String!]!) {
    getPersonByPartyIds(partyIds: $partyIds) {
      status
      partyId
      fullName
      phone
      address
      gender
      birthDate
      email
      personalTitle
      imageUrl
      identityNumber
      id
      addressModel {
        id
        addressInfo
        provinceGeoId
        districtGeoId
        wardGeoId
        provinceName
        districtName
        wardName
        isDefault
      }
    }
  }
`;

export const SEARCH_COMPANY = gql`
  query SearchCompany($keyword: String, $orgId: String!, $limit: Int) {
    searchCompany(keyword: $keyword, orgId: $orgId, limit: $limit) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;
export const GET_CUSTOMER_BY_ID = gql`
  query GetCustomerById($id: String!) {
    getCustomerById(id: $id) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;

export const SEARCH_CUSTOMER = gql`
  query SearchCustomers(
    $keyword: String
    $type: String
    $startCreatedDate: DateCustom
    $endCreatedDate: DateCustom
    $memberLevel: String
    $partnerId: String!
    $currentPage: Int!
    $pageSize: Int!
  ) {
    searchCustomers(
      keyword: $keyword
      type: $type
      startCreatedDate: $startCreatedDate
      endCreatedDate: $endCreatedDate
      memberLevel: $memberLevel
      partnerId: $partnerId
      currentPage: $currentPage
      pageSize: $pageSize
    ) {
      totalPages
      totalElements
      currentPage
      content {
        id
        name
        address
        gender
        identityNumber
        birthDate
        email
        phone
        createdStamp
        createdBy
        memberLevel
      }
    }
  }
`;

export const SEARCH_EMPLOYEES = gql`
  query SearchEmployees(
    $keyword: String
    $positionShortName: String
    $partnerId: String
  ) {
    searchEmployees(
      keyword: $keyword
      positionShortName: $positionShortName
      partnerId: $partnerId
    ) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;

export const GET_POSTIONS_BY_EMPLOYEES_ID = gql`
  query GetPositionsByEmployeeId($employeeId: String!, $partnerId: String!) {
    getPositionsByEmployeeId(employeeId: $employeeId, partnerId: $partnerId)
  }
`;

export const GET_STORE_CHANEL_IDS_BY_EMPLOYESS_ID = gql`
  query GetStoreChannelIdsByEmployeeId(
    $employeeId: String!
    $partnerId: String!
  ) {
    getStoreChannelIdsByEmployeeId(
      employeeId: $employeeId
      partnerId: $partnerId
    )
  }
`;

export const GET_EMPLOYEES_BY_STORE_CHANEL_ID = gql`
  query GetEmployeesByStoreChannelId(
    $storeChannelId: String!
    $partnerId: String!
  ) {
    getEmployeesByStoreChannelId(
      storeChannelId: $storeChannelId
      partnerId: $partnerId
    ) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;

export const GET_COMPANY_BY_CONTACT_INFO_ID = gql`
  query GetCompanyByContactInfoId($contactId: String!, $partnerId: String!) {
    getCompanyByContactInfoId(contactId: $contactId, partnerId: $partnerId) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;
export const GET_CONTACT_INFOS_BY_COMPANY_ID = gql`
  query GetContactInfosByCompanyId($companyId: String!, $partnerId: String!) {
    getContactInfosByCompanyId(companyId: $companyId, partnerId: $partnerId) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;
export const GET_PROVINCES = gql`
  query GetProvinces($version: Int) {
    getProvinces(version: $version) {
      geoId
      geoTypeId
      geoName
      geoNameLocal
      geoCode
      geoSecCode
      abbreviation
      vnPostId
      jtExpressId
      vnPostRegion
      inetId
      lastUpdatedStamp
      lastUpdatedTxStamp
      createdStamp
      createdTxStamp
    }
  }
`;
export const GET_WARDS_BY_PROVINCEID = gql`
  query getWardsByProvinceId($provinceId: String!) {
    getWardsByProvinceId(provinceId: $provinceId) {
      geoId
      geoTypeId
      geoName
      geoNameLocal
      geoCode
      geoSecCode
      abbreviation
      vnPostId
      vnnicId
      jtExpressId
      vnPostRegion
      inetId
      lastUpdatedStamp
      lastUpdatedTxStamp
      createdStamp
      createdTxStamp
      version
    }
  }
`;
export const GET_DISTRICTS = gql`
  query GetDistricts($provinceId: String!) {
    getDistricts(provinceId: $provinceId) {
      geoId
      geoTypeId
      geoName
      geoNameLocal
      geoCode
      geoSecCode
      abbreviation
      vnPostId
      jtExpressId
      vnPostRegion
      inetId
      lastUpdatedStamp
      lastUpdatedTxStamp
      createdStamp
      createdTxStamp
    }
  }
`;
export const GET_WARDS = gql`
  query GetWards($districtId: String!) {
    getWards(districtId: $districtId) {
      geoId
      geoTypeId
      geoName
      geoNameLocal
      geoCode
      geoSecCode
      abbreviation
      vnPostId
      jtExpressId
      vnPostRegion
      inetId
      lastUpdatedStamp
      lastUpdatedTxStamp
      createdStamp
      createdTxStamp
    }
  }
`;
export const GET_PERSON_BY_PARTY_ID = gql`
  query GetPersonByPartyId($partyId: String!) {
    getPersonByPartyId(partyId: $partyId) {
      status
      partyId
      fullName
      phone
      address
      gender
      birthDate
      email
      personalTitle
      imageUrl
      identityNumber
      id
      addressModel {
        id
        addressInfo
        provinceGeoId
        districtGeoId
        wardGeoId
        provinceName
        districtName
        wardName
        isDefault
      }
    }
  }
`;
export const GET_VAT_INFO_BY_OWNER_PARTYID = gql`
  query GetVatInfoByOwnerPartyId($ownerPartyId: String!) {
    getVatInfoByOwnerPartyId(ownerPartyId: $ownerPartyId) {
      id
      company
      taxCode
      address
      invoiceReceiveEmail1
      invoiceReceiveEmail2
      ownerPartyId
      createdStamp
      updatedStamp
      updatedBy
      createdBy
    }
  }
`;
