# Image Service

The Image Service manages image and media assets, including product images, user avatars, marketing materials, and media optimization.

## Table of Contents

- [Overview](#overview)
- [Image Management](#image-management)
- [Image Processing](#image-processing)
- [Media Optimization](#media-optimization)
- [Best Practices](#best-practices)

## Overview

The Image Service provides comprehensive media management functionality:

- **Image Upload**: Handle image uploads with validation and processing
- **Image Transformation**: Resize, crop, and optimize images automatically
- **Media Storage**: Manage image storage across CDN and cloud services
- **Image Variants**: Generate multiple sizes and formats for different use cases
- **Performance Optimization**: Optimize images for web delivery and mobile devices

## Image Management

### Image Data Structure

```typescript
interface ImageAsset {
  id: string;
  originalUrl: string;
  variants: ImageVariant[];
  metadata: ImageMetadata;
  uploadedBy: string;
  uploadedDate: Date;
  tags: string[];
  category: ImageCategory;
  status: ImageStatus;
}

interface ImageVariant {
  size: ImageSize;
  url: string;
  width: number;
  height: number;
  format: ImageFormat;
  fileSize: number;
}

interface ImageMetadata {
  originalFileName: string;
  mimeType: string;
  fileSize: number;
  dimensions: {
    width: number;
    height: number;
  };
  colorProfile?: string;
  exifData?: Record<string, any>;
}

type ImageSize = 
  | 'thumbnail'    // 150x150
  | 'small'        // 300x300
  | 'medium'       // 600x600
  | 'large'        // 1200x1200
  | 'original';

type ImageFormat = 'jpeg' | 'png' | 'webp' | 'avif';

type ImageCategory = 
  | 'product'
  | 'avatar'
  | 'banner'
  | 'marketing'
  | 'document'
  | 'other';

type ImageStatus = 
  | 'uploading'
  | 'processing'
  | 'ready'
  | 'failed'
  | 'deleted';
```

### Image Operations

```typescript
class ImageManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async uploadImage(file: File, options: UploadImageOptions = {}) {
    try {
      // Validate image file
      const validationErrors = this.validateImageFile(file);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Upload image (placeholder - actual implementation would use SDK method)
      const uploadResult = await this.uploadImageAPI(file, options);
      
      console.log('Image uploaded:', {
        id: uploadResult.id,
        originalUrl: uploadResult.originalUrl,
        status: uploadResult.status
      });
      
      return uploadResult;
    } catch (error) {
      console.error('Image upload failed:', error);
      throw error;
    }
  }

  async getImageById(imageId: string): Promise<ImageAsset> {
    try {
      const image = await this.getImageAPI(imageId);
      return image;
    } catch (error) {
      console.error('Failed to fetch image:', error);
      throw error;
    }
  }

  async deleteImage(imageId: string): Promise<void> {
    try {
      await this.deleteImageAPI(imageId);
      console.log(`Image ${imageId} deleted successfully`);
    } catch (error) {
      console.error('Failed to delete image:', error);
      throw error;
    }
  }

  async getImagesByCategory(category: ImageCategory, options: PaginationOptions = {}) {
    try {
      const images = await this.getImagesByCategoryAPI(category, options);
      console.log(`Found ${images.length} images in category ${category}`);
      return images;
    } catch (error) {
      console.error('Failed to fetch images by category:', error);
      throw error;
    }
  }

  private validateImageFile(file: File): string[] {
    const errors: string[] = [];
    
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      errors.push('Invalid file type. Only JPEG, PNG, and WebP are allowed');
    }
    
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push('File size exceeds 10MB limit');
    }
    
    // Check file name
    if (!file.name || file.name.trim().length === 0) {
      errors.push('File name is required');
    }
    
    return errors;
  }

  // Placeholder methods - these would be actual SDK calls
  private async uploadImageAPI(file: File, options: UploadImageOptions): Promise<ImageAsset> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async getImageAPI(imageId: string): Promise<ImageAsset> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async deleteImageAPI(imageId: string): Promise<void> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async getImagesByCategoryAPI(category: ImageCategory, options: PaginationOptions): Promise<ImageAsset[]> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }
}

interface UploadImageOptions {
  category?: ImageCategory;
  tags?: string[];
  generateVariants?: boolean;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```

## Image Processing

### Image Transformation

```typescript
class ImageProcessor {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async generateImageVariants(imageId: string, sizes: ImageSize[] = ['thumbnail', 'small', 'medium', 'large']) {
    try {
      const variants = await Promise.all(
        sizes.map(size => this.generateImageVariant(imageId, size))
      );

      console.log(`Generated ${variants.length} variants for image ${imageId}`);
      return variants;
    } catch (error) {
      console.error('Failed to generate image variants:', error);
      throw error;
    }
  }

  async resizeImage(imageId: string, width: number, height: number, options: ResizeOptions = {}) {
    try {
      const resizedImage = await this.resizeImageAPI(imageId, width, height, options);
      
      console.log('Image resized:', {
        originalId: imageId,
        newDimensions: `${width}x${height}`,
        url: resizedImage.url
      });
      
      return resizedImage;
    } catch (error) {
      console.error('Image resize failed:', error);
      throw error;
    }
  }

  async optimizeImage(imageId: string, options: OptimizationOptions = {}) {
    try {
      const optimizedImage = await this.optimizeImageAPI(imageId, options);
      
      const compressionRatio = ((optimizedImage.originalSize - optimizedImage.optimizedSize) / optimizedImage.originalSize) * 100;
      
      console.log('Image optimized:', {
        imageId,
        originalSize: optimizedImage.originalSize,
        optimizedSize: optimizedImage.optimizedSize,
        compressionRatio: `${compressionRatio.toFixed(1)}%`
      });
      
      return optimizedImage;
    } catch (error) {
      console.error('Image optimization failed:', error);
      throw error;
    }
  }

  async convertImageFormat(imageId: string, targetFormat: ImageFormat, quality: number = 85) {
    try {
      const convertedImage = await this.convertImageFormatAPI(imageId, targetFormat, quality);
      
      console.log('Image format converted:', {
        imageId,
        targetFormat,
        url: convertedImage.url
      });
      
      return convertedImage;
    } catch (error) {
      console.error('Image format conversion failed:', error);
      throw error;
    }
  }

  private async generateImageVariant(imageId: string, size: ImageSize): Promise<ImageVariant> {
    const dimensions = this.getSizeDimensions(size);
    return await this.resizeImageAPI(imageId, dimensions.width, dimensions.height, {
      maintainAspectRatio: true,
      quality: 85
    });
  }

  private getSizeDimensions(size: ImageSize): { width: number; height: number } {
    const dimensionMap = {
      thumbnail: { width: 150, height: 150 },
      small: { width: 300, height: 300 },
      medium: { width: 600, height: 600 },
      large: { width: 1200, height: 1200 },
      original: { width: 0, height: 0 } // Original dimensions
    };
    
    return dimensionMap[size];
  }

  // Placeholder methods
  private async resizeImageAPI(imageId: string, width: number, height: number, options: ResizeOptions): Promise<ImageVariant> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async optimizeImageAPI(imageId: string, options: OptimizationOptions): Promise<any> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async convertImageFormatAPI(imageId: string, format: ImageFormat, quality: number): Promise<ImageVariant> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }
}

interface ResizeOptions {
  maintainAspectRatio?: boolean;
  quality?: number;
  format?: ImageFormat;
  cropMode?: 'center' | 'top' | 'bottom' | 'left' | 'right';
}

interface OptimizationOptions {
  quality?: number;
  progressive?: boolean;
  stripMetadata?: boolean;
  format?: ImageFormat;
}
```

## Media Optimization

### Performance Optimization

```typescript
class MediaOptimizer {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async optimizeForWeb(imageId: string): Promise<ImageAsset> {
    try {
      // Generate web-optimized variants
      const webVariants = await this.generateWebVariants(imageId);
      
      // Convert to modern formats
      const modernFormats = await this.generateModernFormats(imageId);
      
      const optimizedAsset = {
        id: imageId,
        webVariants,
        modernFormats,
        optimizationDate: new Date()
      };

      console.log('Image optimized for web:', {
        imageId,
        variantCount: webVariants.length,
        formatCount: modernFormats.length
      });

      return optimizedAsset as ImageAsset;
    } catch (error) {
      console.error('Web optimization failed:', error);
      throw error;
    }
  }

  async generateResponsiveImageSet(imageId: string): Promise<ResponsiveImageSet> {
    try {
      const breakpoints = [320, 480, 768, 1024, 1200, 1920];
      
      const variants = await Promise.all(
        breakpoints.map(async (width) => {
          const variant = await this.resizeForBreakpoint(imageId, width);
          return {
            width,
            url: variant.url,
            format: variant.format
          };
        })
      );

      const responsiveSet = {
        imageId,
        variants,
        srcSet: this.generateSrcSet(variants),
        sizes: this.generateSizesAttribute()
      };

      return responsiveSet;
    } catch (error) {
      console.error('Failed to generate responsive image set:', error);
      throw error;
    }
  }

  async optimizeForMobile(imageId: string): Promise<ImageAsset> {
    try {
      // Generate mobile-specific variants
      const mobileVariants = await Promise.all([
        this.resizeForBreakpoint(imageId, 320), // Mobile portrait
        this.resizeForBreakpoint(imageId, 480), // Mobile landscape
        this.resizeForBreakpoint(imageId, 768)  // Tablet
      ]);

      // Convert to WebP for better compression
      const webpVariants = await Promise.all(
        mobileVariants.map(variant => 
          this.convertToWebP(variant, { quality: 80 })
        )
      );

      console.log('Image optimized for mobile:', {
        imageId,
        mobileVariants: mobileVariants.length,
        webpVariants: webpVariants.length
      });

      return {
        id: imageId,
        mobileVariants,
        webpVariants
      } as ImageAsset;
    } catch (error) {
      console.error('Mobile optimization failed:', error);
      throw error;
    }
  }

  private async generateWebVariants(imageId: string): Promise<ImageVariant[]> {
    const webSizes: ImageSize[] = ['thumbnail', 'small', 'medium', 'large'];
    return await Promise.all(
      webSizes.map(size => this.generateOptimizedVariant(imageId, size))
    );
  }

  private async generateModernFormats(imageId: string): Promise<ImageVariant[]> {
    const formats: ImageFormat[] = ['webp', 'avif'];
    return await Promise.all(
      formats.map(format => this.convertToFormat(imageId, format))
    );
  }

  private async resizeForBreakpoint(imageId: string, maxWidth: number): Promise<ImageVariant> {
    // Placeholder implementation
    return {
      size: 'medium',
      url: `https://example.com/images/${imageId}_${maxWidth}w.jpg`,
      width: maxWidth,
      height: Math.round(maxWidth * 0.75), // Assume 4:3 aspect ratio
      format: 'jpeg',
      fileSize: maxWidth * 100 // Rough estimate
    };
  }

  private async generateOptimizedVariant(imageId: string, size: ImageSize): Promise<ImageVariant> {
    // Placeholder implementation
    const dimensions = this.getSizeDimensions(size);
    return {
      size,
      url: `https://example.com/images/${imageId}_${size}.jpg`,
      width: dimensions.width,
      height: dimensions.height,
      format: 'jpeg',
      fileSize: dimensions.width * dimensions.height * 0.1
    };
  }

  private async convertToFormat(imageId: string, format: ImageFormat): Promise<ImageVariant> {
    // Placeholder implementation
    return {
      size: 'medium',
      url: `https://example.com/images/${imageId}.${format}`,
      width: 600,
      height: 600,
      format,
      fileSize: 50000
    };
  }

  private async convertToWebP(variant: ImageVariant, options: { quality: number }): Promise<ImageVariant> {
    return {
      ...variant,
      format: 'webp',
      url: variant.url.replace(/\.(jpg|jpeg|png)$/, '.webp'),
      fileSize: Math.round(variant.fileSize * 0.7) // WebP typically 30% smaller
    };
  }

  private getSizeDimensions(size: ImageSize): { width: number; height: number } {
    const dimensionMap = {
      thumbnail: { width: 150, height: 150 },
      small: { width: 300, height: 300 },
      medium: { width: 600, height: 600 },
      large: { width: 1200, height: 1200 },
      original: { width: 0, height: 0 }
    };
    
    return dimensionMap[size];
  }

  private generateSrcSet(variants: Array<{ width: number; url: string }>): string {
    return variants
      .map(variant => `${variant.url} ${variant.width}w`)
      .join(', ');
  }

  private generateSizesAttribute(): string {
    return '(max-width: 320px) 280px, (max-width: 480px) 440px, (max-width: 768px) 728px, (max-width: 1024px) 984px, 1200px';
  }
}

interface ResponsiveImageSet {
  imageId: string;
  variants: Array<{
    width: number;
    url: string;
    format: ImageFormat;
  }>;
  srcSet: string;
  sizes: string;
}
```

## Best Practices

### 1. Image Upload Validation

```typescript
// ✅ Good: Comprehensive image validation
class ImageValidator {
  static validateImageFile(file: File): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // File type validation
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/avif'];
    if (!allowedTypes.includes(file.type)) {
      errors.push(`Invalid file type: ${file.type}. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // File size validation
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      errors.push(`File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(maxSize)})`);
    }

    // File size warning for large files
    const warningSize = 2 * 1024 * 1024; // 2MB
    if (file.size > warningSize && file.size <= maxSize) {
      warnings.push(`Large file size (${this.formatFileSize(file.size)}) may affect upload performance`);
    }

    // File name validation
    if (!file.name || file.name.trim().length === 0) {
      errors.push('File name is required');
    }

    // File name format validation
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(file.name)) {
      errors.push('File name contains invalid characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static async validateImageDimensions(file: File, requirements?: DimensionRequirements): Promise<ValidationResult> {
    return new Promise((resolve) => {
      const img = new Image();
      const errors: string[] = [];
      
      img.onload = () => {
        if (requirements) {
          if (requirements.minWidth && img.width < requirements.minWidth) {
            errors.push(`Image width (${img.width}px) is below minimum required (${requirements.minWidth}px)`);
          }
          
          if (requirements.maxWidth && img.width > requirements.maxWidth) {
            errors.push(`Image width (${img.width}px) exceeds maximum allowed (${requirements.maxWidth}px)`);
          }
          
          if (requirements.minHeight && img.height < requirements.minHeight) {
            errors.push(`Image height (${img.height}px) is below minimum required (${requirements.minHeight}px)`);
          }
          
          if (requirements.maxHeight && img.height > requirements.maxHeight) {
            errors.push(`Image height (${img.height}px) exceeds maximum allowed (${requirements.maxHeight}px)`);
          }

          if (requirements.aspectRatio) {
            const actualRatio = img.width / img.height;
            const tolerance = 0.1;
            if (Math.abs(actualRatio - requirements.aspectRatio) > tolerance) {
              errors.push(`Image aspect ratio (${actualRatio.toFixed(2)}) does not match required ratio (${requirements.aspectRatio})`);
            }
          }
        }

        resolve({
          isValid: errors.length === 0,
          errors,
          warnings: [],
          dimensions: { width: img.width, height: img.height }
        });
      };

      img.onerror = () => {
        resolve({
          isValid: false,
          errors: ['Unable to load image for dimension validation'],
          warnings: []
        });
      };

      img.src = URL.createObjectURL(file);
    });
  }

  private static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  dimensions?: { width: number; height: number };
}

interface DimensionRequirements {
  minWidth?: number;
  maxWidth?: number;
  minHeight?: number;
  maxHeight?: number;
  aspectRatio?: number;
}
```

### 2. Performance Optimization

```typescript
// ✅ Good: Implement lazy loading and progressive enhancement
class ImagePerformanceOptimizer {
  static generatePictureElement(imageAsset: ImageAsset, alt: string, className?: string): string {
    const webpVariant = imageAsset.variants.find(v => v.format === 'webp');
    const jpegVariant = imageAsset.variants.find(v => v.format === 'jpeg');
    
    return `
      <picture${className ? ` class="${className}"` : ''}>
        ${webpVariant ? `<source srcset="${webpVariant.url}" type="image/webp">` : ''}
        <img src="${jpegVariant?.url || imageAsset.originalUrl}" 
             alt="${alt}" 
             loading="lazy"
             decoding="async">
      </picture>
    `;
  }

  static generateResponsiveImage(responsiveSet: ResponsiveImageSet, alt: string): string {
    return `
      <img src="${responsiveSet.variants[0].url}"
           srcset="${responsiveSet.srcSet}"
           sizes="${responsiveSet.sizes}"
           alt="${alt}"
           loading="lazy"
           decoding="async">
    `;
  }

  static preloadCriticalImages(imageUrls: string[]): void {
    imageUrls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      document.head.appendChild(link);
    });
  }
}
```

### 3. Error Handling

```typescript
// ✅ Good: Handle image service errors gracefully
async function safeImageOperation<T>(operation: () => Promise<T>): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('Image not found');
      return null;
    } else if (error.response?.status === 413) {
      throw new Error('Image file too large');
    } else if (error.response?.status === 415) {
      throw new Error('Unsupported image format');
    } else if (error.response?.status === 422) {
      throw new Error('Invalid image data');
    }
    throw error;
  }
}
```

---

**Next Steps:**
- [Upload Service](./upload.md) - File upload handling
- [Portal Service](./portal.md) - Administrative portal operations
- [Advanced Topics](../advanced/README.md) - Advanced SDK usage patterns
