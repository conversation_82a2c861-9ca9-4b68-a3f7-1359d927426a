// tests/lib/store/store.test.ts

import sdk from "../../setup";

describe("StoreService", () => {
  let storeService: any;

  beforeAll(() => {
    storeService = sdk.store;
  });

  describe("createStore", () => {
    it("should create a store with mock data", async () => {
      const partnerId = "TEST_PARTNER";
      const storeName = "Test Store";

      const result = await storeService.createStore(partnerId, storeName);

      expect(result).toBeDefined();
      expect(result.name).toBe(storeName);
      expect(result.partyId).toBe(partnerId);
      expect(result.type).toBe("pos");
      expect(result.enable).toBe(true);
      expect(result.warehouses).toBeInstanceOf(Array);
      expect(result.warehouses.length).toBeGreaterThan(0);
      expect(result.__typename).toBe("ProductStore");
    });

    it("should handle different store names", async () => {
      const partnerId = "FOX";
      const storeName = "E-commerce Main Store";

      const result = await storeService.createStore(partnerId, storeName);

      expect(result.name).toBe(storeName);
      expect(result.partyId).toBe(partnerId);
      expect(result.id).toMatch(/^store-\d+$/);
    });
  });

  describe("getStoreChannelByEmpId", () => {
    it("should return array of store channels", async () => {
      const saleId = "emp-123";

      const result = await storeService.getStoreChannelByEmpId(saleId);

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBeGreaterThan(0);

      // Check first store structure
      const firstStore = result[0];
      expect(firstStore).toHaveProperty("id");
      expect(firstStore).toHaveProperty("name");
      expect(firstStore).toHaveProperty("type");
      expect(firstStore).toHaveProperty("enable");
      expect(firstStore).toHaveProperty("partyId");
      expect(firstStore).toHaveProperty("warehouses");
      expect(firstStore).toHaveProperty("warehouseIdDefault");
      expect(firstStore).toHaveProperty("shippingCompanies");
      expect(firstStore).toHaveProperty("__typename", "ProductStore");
    });

    it("should return different store types", async () => {
      const saleId = "emp-456";

      const result = await storeService.getStoreChannelByEmpId(saleId);

      const storeTypes = result.map((store: any) => store.type);
      expect(storeTypes).toContain("pos");
      expect(storeTypes).toContain("ecommerce");
    });

    it("should handle high permission users (mock all channels)", async () => {
      const saleId = "admin-user";

      const result = await storeService.getStoreChannelByEmpId(saleId);

      // Should return multiple channels for admin users
      expect(result.length).toBeGreaterThanOrEqual(2);

      // Check that we have both POS and E-commerce stores
      const hasPos = result.some((store: any) => store.type === "pos");
      const hasEcommerce = result.some((store: any) => store.type === "ecommerce");

      expect(hasPos).toBe(true);
      expect(hasEcommerce).toBe(true);
    });
  });

  describe("Store Service Properties", () => {
    it("should have correct service properties", () => {
      expect(storeService.orgId).toBeDefined();
      expect(storeService.storeId).toBeDefined();
      expect(typeof storeService.setToken).toBe("function");
      expect(typeof storeService.setStoreId).toBe("function");
      expect(typeof storeService.setOrgId).toBe("function");
    });
  });

  // Integration tests với real API (comment out để tránh gọi API thật khi test)
  // Uncomment khi muốn test với data thật

  // it("createStore integration test", async () => {
  //   const partnerId = "FOX";
  //   const storeName = `Test-Store-${Date.now()}`;
  //
  //   const response = await storeService.createStore(partnerId, storeName);
  //   console.log("Create Store Response:", response);
  //   expect(response).toBeDefined();
  //   expect(response.name).toBe(storeName);
  // }, 10000);

  // it("getStoreChannelByEmpId integration test", async () => {
  //   const saleId = "real-employee-id";
  //
  //   const response = await storeService.getStoreChannelByEmpId(saleId);
  //   console.log("Store Channels Response:", response);
  //   expect(response).toBeInstanceOf(Array);
  // }, 10000);
});
