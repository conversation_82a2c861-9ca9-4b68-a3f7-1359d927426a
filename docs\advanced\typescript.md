# TypeScript Support

Comprehensive guide to using TypeScript with the Longvan Storefront JavaScript Client SDK, including advanced type patterns, generic utilities, and type-safe development practices.

## Table of Contents

- [Overview](#overview)
- [Type Definitions](#type-definitions)
- [Generic Patterns](#generic-patterns)
- [Type Guards](#type-guards)
- [Advanced Type Utilities](#advanced-type-utilities)
- [Type-Safe API Calls](#type-safe-api-calls)
- [Best Practices](#best-practices)

## Overview

The SDK is built with TypeScript and provides comprehensive type definitions for all services, methods, and data structures. This ensures type safety, better IDE support, and improved developer experience.

## Type Definitions

### Core SDK Types

```typescript
// Main SDK interface
interface SDK {
  auth: AuthService;
  product: ProductService;
  order: OrderService;
  user: UserService;
  payment: PaymentService;
  crm: CRMService;
  warehouse: WarehouseService;
  warehouseV2: WarehouseV2Service;
  computing: ComputingService;
  campaign: CampaignService;
  image: ImageService;
  upload: UploadService;
  
  setToken(token: string): void;
  setStoreId(storeId: string): void;
  setOrgId(orgId: string): void;
}

// Environment configuration
type Environment = 'dev' | 'live';

// SDK constructor
interface SDKConstructor {
  new (orgId: string, storeId: string, environment: Environment): SDK;
}
```

### Service Response Types

```typescript
// Generic service response wrapper
interface ServiceResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

// Paginated response
interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// API error response
interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}
```

### Entity Types

```typescript
// Product entity
interface Product {
  id: string;
  title: string;
  description: string;
  sku: string;
  price: number;
  available: boolean;
  categories: Category[];
  featuredImage: string;
  subType: string;
  brandId?: string;
  tags?: string[];
  variants?: ProductVariant[];
  attributes?: ProductAttribute[];
  createdDate: Date;
  updatedDate: Date;
}

// Order entity
interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  status: OrderStatus;
  lineItems: LineItem[];
  subtotal: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  total: number;
  currency: string;
  shippingAddress?: Address;
  billingAddress?: Address;
  paymentMethod?: PaymentMethod;
  createdDate: Date;
  updatedDate: Date;
}

// User entity
interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone?: string;
  address?: string;
  birthDate?: Date;
  gender?: string;
  avatarUrl?: string;
  memberLevel: string;
  isActive: boolean;
  createdDate: Date;
  lastLoginDate?: Date;
}
```

## Generic Patterns

### Generic Service Methods

```typescript
// Generic CRUD operations
interface CRUDService<T, CreateRequest, UpdateRequest> {
  create(data: CreateRequest): Promise<T>;
  getById(id: string): Promise<T>;
  update(id: string, data: UpdateRequest): Promise<T>;
  delete(id: string): Promise<void>;
  list(options?: ListOptions): Promise<PaginatedResponse<T>>;
}

// Example implementation
class GenericProductService implements CRUDService<Product, CreateProductRequest, UpdateProductRequest> {
  constructor(private sdk: SDK) {}

  async create(data: CreateProductRequest): Promise<Product> {
    // Implementation
    throw new Error('Not implemented');
  }

  async getById(id: string): Promise<Product> {
    return await this.sdk.product.getProductById(id);
  }

  async update(id: string, data: UpdateProductRequest): Promise<Product> {
    // Implementation
    throw new Error('Not implemented');
  }

  async delete(id: string): Promise<void> {
    // Implementation
    throw new Error('Not implemented');
  }

  async list(options?: ListOptions): Promise<PaginatedResponse<Product>> {
    // Implementation
    throw new Error('Not implemented');
  }
}
```

### Generic Query Builder

```typescript
// Generic query builder for type-safe filtering
class QueryBuilder<T> {
  private filters: Partial<T> = {};
  private sortBy?: keyof T;
  private sortOrder: 'asc' | 'desc' = 'asc';
  private pageSize = 20;
  private currentPage = 1;

  where<K extends keyof T>(field: K, value: T[K]): this {
    this.filters[field] = value;
    return this;
  }

  orderBy<K extends keyof T>(field: K, order: 'asc' | 'desc' = 'asc'): this {
    this.sortBy = field;
    this.sortOrder = order;
    return this;
  }

  page(page: number, size: number = 20): this {
    this.currentPage = page;
    this.pageSize = size;
    return this;
  }

  build(): QueryOptions<T> {
    return {
      filters: this.filters,
      sortBy: this.sortBy,
      sortOrder: this.sortOrder,
      currentPage: this.currentPage,
      pageSize: this.pageSize
    };
  }
}

interface QueryOptions<T> {
  filters: Partial<T>;
  sortBy?: keyof T;
  sortOrder: 'asc' | 'desc';
  currentPage: number;
  pageSize: number;
}

// Usage
const productQuery = new QueryBuilder<Product>()
  .where('available', true)
  .where('price', 100000)
  .orderBy('createdDate', 'desc')
  .page(1, 10)
  .build();
```

### Generic Repository Pattern

```typescript
// Generic repository interface
interface Repository<T, ID = string> {
  findById(id: ID): Promise<T | null>;
  findAll(options?: QueryOptions<T>): Promise<PaginatedResponse<T>>;
  save(entity: T): Promise<T>;
  delete(id: ID): Promise<void>;
}

// Product repository implementation
class ProductRepository implements Repository<Product> {
  constructor(private sdk: SDK) {}

  async findById(id: string): Promise<Product | null> {
    try {
      return await this.sdk.product.getProductById(id);
    } catch (error) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  async findAll(options?: QueryOptions<Product>): Promise<PaginatedResponse<Product>> {
    const products = await this.sdk.product.getSimpleProducts({
      currentPage: options?.currentPage || 1,
      maxResult: options?.pageSize || 20,
      // Map other options to SDK parameters
    });

    // Transform to paginated response
    return {
      content: products,
      totalElements: products.length, // This would come from API
      totalPages: Math.ceil(products.length / (options?.pageSize || 20)),
      currentPage: options?.currentPage || 1,
      pageSize: options?.pageSize || 20,
      hasNext: false, // Calculate based on actual data
      hasPrevious: (options?.currentPage || 1) > 1
    };
  }

  async save(product: Product): Promise<Product> {
    // Implementation for create/update
    throw new Error('Not implemented');
  }

  async delete(id: string): Promise<void> {
    // Implementation for delete
    throw new Error('Not implemented');
  }
}
```

## Type Guards

### Runtime Type Checking

```typescript
// Type guard functions
function isProduct(obj: any): obj is Product {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.price === 'number' &&
    typeof obj.available === 'boolean';
}

function isOrder(obj: any): obj is Order {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.orderNumber === 'string' &&
    typeof obj.customerId === 'string' &&
    Array.isArray(obj.lineItems);
}

function isUser(obj: any): obj is User {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.username === 'string' &&
    typeof obj.email === 'string';
}

// Generic type guard creator
function createTypeGuard<T>(
  validator: (obj: any) => boolean
): (obj: any) => obj is T {
  return (obj: any): obj is T => validator(obj);
}

// Usage
const isValidProduct = createTypeGuard<Product>((obj) => 
  isProduct(obj) && obj.price > 0
);

// Safe type checking
function processApiResponse(response: any) {
  if (isProduct(response)) {
    // TypeScript knows response is Product
    console.log(`Product: ${response.title} - $${response.price}`);
  } else if (isOrder(response)) {
    // TypeScript knows response is Order
    console.log(`Order: ${response.orderNumber} - Total: $${response.total}`);
  } else {
    console.log('Unknown response type');
  }
}
```

### Discriminated Unions

```typescript
// Discriminated union for different response types
type APIResponse = 
  | { type: 'success'; data: any }
  | { type: 'error'; error: APIError }
  | { type: 'loading' };

// Type-safe response handler
function handleAPIResponse(response: APIResponse) {
  switch (response.type) {
    case 'success':
      // TypeScript knows response.data exists
      console.log('Success:', response.data);
      break;
    case 'error':
      // TypeScript knows response.error exists
      console.error('Error:', response.error.message);
      break;
    case 'loading':
      // TypeScript knows this is loading state
      console.log('Loading...');
      break;
    default:
      // TypeScript ensures exhaustive checking
      const _exhaustive: never = response;
      break;
  }
}

// Service status discriminated union
type ServiceStatus = 
  | { status: 'idle' }
  | { status: 'loading'; progress?: number }
  | { status: 'success'; data: any }
  | { status: 'error'; error: string };

class TypeSafeService {
  private status: ServiceStatus = { status: 'idle' };

  getStatus(): ServiceStatus {
    return this.status;
  }

  setLoading(progress?: number): void {
    this.status = { status: 'loading', progress };
  }

  setSuccess(data: any): void {
    this.status = { status: 'success', data };
  }

  setError(error: string): void {
    this.status = { status: 'error', error };
  }
}
```

## Advanced Type Utilities

### Utility Types

```typescript
// Extract specific properties
type ProductSummary = Pick<Product, 'id' | 'title' | 'price' | 'available'>;

// Make properties optional
type PartialProduct = Partial<Product>;

// Make properties required
type RequiredProductFields = Required<Pick<Product, 'id' | 'title' | 'price'>>;

// Exclude properties
type ProductWithoutDates = Omit<Product, 'createdDate' | 'updatedDate'>;

// Create update types
type UpdateProductRequest = Partial<Omit<Product, 'id' | 'createdDate' | 'updatedDate'>>;

// Extract function return types
type ProductServiceMethods = typeof SDK.prototype.product;
type GetProductByIdReturn = ReturnType<ProductServiceMethods['getProductById']>;

// Create mapped types
type ProductFields = {
  [K in keyof Product]: Product[K];
};

// Conditional types
type NonNullable<T> = T extends null | undefined ? never : T;
type APIResult<T> = T extends Promise<infer U> ? U : T;
```

### Custom Utility Types

```typescript
// Deep partial type
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Deep readonly type
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

// Extract array element type
type ArrayElement<T> = T extends (infer U)[] ? U : never;
type ProductArrayElement = ArrayElement<Product[]>; // Product

// Function parameter types
type Parameters<T> = T extends (...args: infer P) => any ? P : never;
type GetProductParams = Parameters<typeof sdk.product.getProductById>; // [string]

// Create branded types for IDs
type Brand<T, B> = T & { __brand: B };
type ProductId = Brand<string, 'ProductId'>;
type OrderId = Brand<string, 'OrderId'>;
type UserId = Brand<string, 'UserId'>;

// Type-safe ID creation
function createProductId(id: string): ProductId {
  return id as ProductId;
}

function createOrderId(id: string): OrderId {
  return id as OrderId;
}

// Usage with branded types
async function getProductSafely(productId: ProductId): Promise<Product> {
  return await sdk.product.getProductById(productId);
}

// This would cause a TypeScript error:
// const orderId = createOrderId('order-123');
// getProductSafely(orderId); // Error: OrderId is not assignable to ProductId
```

## Type-Safe API Calls

### Typed Service Wrapper

```typescript
// Type-safe service wrapper
class TypeSafeSDKWrapper {
  constructor(private sdk: SDK) {}

  // Type-safe product operations
  async getProduct(id: ProductId): Promise<Product> {
    const product = await this.sdk.product.getProductById(id);
    if (!isProduct(product)) {
      throw new Error('Invalid product response');
    }
    return product;
  }

  async getProducts(options: ProductQueryOptions): Promise<PaginatedResponse<Product>> {
    const products = await this.sdk.product.getSimpleProducts(options);
    
    // Validate all products
    const validProducts = products.filter(isProduct);
    if (validProducts.length !== products.length) {
      console.warn('Some products failed validation');
    }

    return {
      content: validProducts,
      totalElements: validProducts.length,
      totalPages: Math.ceil(validProducts.length / (options.maxResult || 20)),
      currentPage: options.currentPage || 1,
      pageSize: options.maxResult || 20,
      hasNext: false,
      hasPrevious: (options.currentPage || 1) > 1
    };
  }

  // Type-safe order operations
  async createOrder(orderData: CreateOrderRequest): Promise<Order> {
    const order = await this.sdk.order.createOrder(
      orderData,
      'web',
      false,
      orderData.createdBy
    );
    
    if (!isOrder(order)) {
      throw new Error('Invalid order response');
    }
    
    return order;
  }
}

interface ProductQueryOptions {
  keyword?: string;
  category?: string;
  priceFrom?: number;
  priceTo?: number;
  currentPage?: number;
  maxResult?: number;
}

interface CreateOrderRequest {
  customer_id: UserId;
  line_items: LineItem[];
  shipping_address?: Address;
  createdBy: UserId;
}
```

### Type-Safe Error Handling

```typescript
// Typed error classes
class SDKError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'SDKError';
  }
}

class ValidationError extends SDKError {
  constructor(
    message: string,
    public field: string,
    public value: any
  ) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

class NetworkError extends SDKError {
  constructor(
    message: string,
    statusCode: number,
    public isRetryable: boolean = false
  ) {
    super(message, 'NETWORK_ERROR', statusCode);
    this.name = 'NetworkError';
  }
}

// Type-safe error handler
function handleSDKError(error: unknown): never {
  if (error instanceof ValidationError) {
    console.error(`Validation error in field ${error.field}:`, error.message);
    throw error;
  } else if (error instanceof NetworkError) {
    console.error(`Network error (${error.statusCode}):`, error.message);
    if (error.isRetryable) {
      console.log('Error is retryable');
    }
    throw error;
  } else if (error instanceof SDKError) {
    console.error(`SDK error (${error.code}):`, error.message);
    throw error;
  } else if (error instanceof Error) {
    console.error('Unknown error:', error.message);
    throw new SDKError(error.message, 'UNKNOWN_ERROR');
  } else {
    console.error('Non-error thrown:', error);
    throw new SDKError('Unknown error occurred', 'UNKNOWN_ERROR');
  }
}
```

## Best Practices

### 1. Strict Type Configuration

```typescript
// tsconfig.json for strict typing
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true
  }
}
```

### 2. Type-Safe Configuration

```typescript
// ✅ Good: Type-safe SDK configuration
interface SDKConfig {
  orgId: string;
  storeId: string;
  environment: Environment;
  timeout?: number;
  retryAttempts?: number;
  apiVersion?: string;
}

class TypeSafeSDK {
  private config: Required<SDKConfig>;
  private sdk: SDK;

  constructor(config: SDKConfig) {
    this.config = {
      timeout: 30000,
      retryAttempts: 3,
      apiVersion: 'v1',
      ...config
    };
    
    this.sdk = new SDK(
      this.config.orgId,
      this.config.storeId,
      this.config.environment
    );
  }

  getConfig(): DeepReadonly<SDKConfig> {
    return this.config;
  }

  updateConfig(updates: Partial<SDKConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Update SDK if necessary
    if (updates.storeId) {
      this.sdk.setStoreId(updates.storeId);
    }
    if (updates.orgId) {
      this.sdk.setOrgId(updates.orgId);
    }
  }
}
```

### 3. Type-Safe Event System

```typescript
// ✅ Good: Type-safe event system
interface EventMap {
  'product:loaded': { product: Product };
  'order:created': { order: Order };
  'user:authenticated': { user: User };
  'error:occurred': { error: SDKError };
}

class TypeSafeEventEmitter {
  private listeners: {
    [K in keyof EventMap]?: Array<(data: EventMap[K]) => void>;
  } = {};

  on<K extends keyof EventMap>(
    event: K,
    listener: (data: EventMap[K]) => void
  ): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event]!.push(listener);
  }

  emit<K extends keyof EventMap>(event: K, data: EventMap[K]): void {
    const eventListeners = this.listeners[event];
    if (eventListeners) {
      eventListeners.forEach(listener => listener(data));
    }
  }

  off<K extends keyof EventMap>(
    event: K,
    listener: (data: EventMap[K]) => void
  ): void {
    const eventListeners = this.listeners[event];
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }
}

// Usage
const eventEmitter = new TypeSafeEventEmitter();

eventEmitter.on('product:loaded', ({ product }) => {
  // TypeScript knows product is of type Product
  console.log(`Product loaded: ${product.title}`);
});

eventEmitter.emit('product:loaded', { product: someProduct });
```

### 4. Type-Safe Builder Pattern

```typescript
// ✅ Good: Type-safe builder pattern
class OrderBuilder {
  private order: Partial<Order> = {};

  setCustomer(customerId: UserId): this {
    this.order.customerId = customerId;
    return this;
  }

  addLineItem(item: LineItem): this {
    if (!this.order.lineItems) {
      this.order.lineItems = [];
    }
    this.order.lineItems.push(item);
    return this;
  }

  setShippingAddress(address: Address): this {
    this.order.shippingAddress = address;
    return this;
  }

  setBillingAddress(address: Address): this {
    this.order.billingAddress = address;
    return this;
  }

  build(): CreateOrderRequest {
    if (!this.order.customerId) {
      throw new Error('Customer ID is required');
    }
    if (!this.order.lineItems || this.order.lineItems.length === 0) {
      throw new Error('At least one line item is required');
    }

    return {
      customer_id: this.order.customerId,
      line_items: this.order.lineItems,
      shipping_address: this.order.shippingAddress,
      createdBy: this.order.customerId // Assuming same as customer
    };
  }
}

// Usage
const orderRequest = new OrderBuilder()
  .setCustomer(createUserId('user-123'))
  .addLineItem({
    quantity: 2,
    product_id: 'product-456',
    input_price: 100000
  })
  .setShippingAddress({
    name: 'John Doe',
    phone: '0123456789',
    address: '123 Main St',
    province_code: 'HCM',
    district_code: 'Q1',
    ward_code: 'P1'
  })
  .build();
```

---

**Next Steps:**
- [Testing](./testing.md) - Testing strategies and patterns
- [Performance](./performance.md) - Performance optimization techniques
- [Best Practices](./best-practices.md) - Development best practices
