import { gql } from "graphql-tag";
export const CREATE_COMPANY = gql`
  mutation CreateCompany(
    $createCompanyRequest: CreateCompanyRequest!
    $createTaxCodeRequest: CreateTaxCodeRequest
    $orgId: String!
    $createdBy: String!
  ) {
    createCompany(
      createCompanyRequest: $createCompanyRequest
      createTaxCodeRequest: $createTaxCodeRequest
      orgId: $orgId
      createdBy: $createdBy
    ) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;

export const UPDATE_COMPANY_INFOR = gql`
  mutation UpdateCompanyInfo(
    $id: String!
    $fieldName: String!
    $valueUpdate: String!
    $orgId: String!
    $updatedBy: String!
  ) {
    updateCompanyInfo(
      id: $id
      fieldName: $fieldName
      valueUpdate: $valueUpdate
      orgId: $orgId
      updatedBy: $updatedBy
    ) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;

export const UPDATE_CUSTOMER_V2 = gql`
  mutation UpdateCustomerV2(
    $id: String!
    $updateCustomerRequest: UpdateCustomerRequest!
    $tenantId: String!
    $updatedBy: String!
  ) {
    updateCustomerV2(
      id: $id
      updateCustomerRequest: $updateCustomerRequest
      tenantId: $tenantId
      updatedBy: $updatedBy
    ) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;

export const CREATE_CUSTOMER_V2 = gql`
  mutation CreateCustomerV2(
    $name: String!
    $phone: String
    $email: String
    $birthDate: DateCustom
    $tenantId: String!
    $createdBy: String
  ) {
    createCustomerV2(
      name: $name
      phone: $phone
      email: $email
      birthDate: $birthDate
      tenantId: $tenantId
      createdBy: $createdBy
    ) {
      id
      name
      address
      gender
      identityNumber
      birthDate
      email
      phone
      createdStamp
      createdBy
      memberLevel
    }
  }
`;
export const CREATE_VAT_INFO = gql`
  mutation CreateVatInfo(
    $company: String!
    $taxCode: String!
    $address: String!
    $invoiceReceiveEmail1: String
    $ownerPartyId: String!
    $createdBy: String!
  ) {
    createVatInfo(
      createVatInfoRequest: {
        company: $company
        taxCode: $taxCode
        address: $address
        invoiceReceiveEmail1: $invoiceReceiveEmail1
        ownerPartyId: $ownerPartyId
      }
      createdBy: $createdBy
    ) {
      id
      company
      taxCode
      address
      invoiceReceiveEmail1
      invoiceReceiveEmail2
      ownerPartyId
      createdStamp
      updatedStamp
      updatedBy
      createdBy
    }
  }
`;
