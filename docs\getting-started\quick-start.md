# Quick Start Guide

Get up and running with the Longvan Storefront JavaScript Client SDK in under 5 minutes.

## 🚀 Installation

```bash
npm install @longvansoftware/storefront-js-client
```

## 🔧 Basic Setup

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

// Initialize the SDK
const sdk = new SDK('your-org-id', 'your-store-id', 'dev');

// Set authentication token (optional for public endpoints)
sdk.setToken('your-access-token');
```

## 🎯 Your First API Call

Let's fetch some products:

```typescript
async function getProducts() {
  try {
    const products = await sdk.product.getSimpleProducts({
      currentPage: 1,
      maxResult: 10
    });
    
    console.log('Products:', products);
    return products;
  } catch (error) {
    console.error('Error fetching products:', error);
  }
}

getProducts();
```

## 🔐 Authentication Flow

### 1. User Login

```typescript
async function loginUser() {
  try {
    const loginResponse = await sdk.auth.login({
      username: '<EMAIL>',
      password: 'password123'
    });
    
    // Store the token for future requests
    sdk.setToken(loginResponse.accessToken);
    
    console.log('Login successful:', loginResponse.fullName);
    return loginResponse;
  } catch (error) {
    console.error('Login failed:', error);
  }
}
```

### 2. User Registration

```typescript
async function registerUser() {
  try {
    await sdk.auth.register({
      username: '<EMAIL>',
      fullName: 'John Doe',
      password: 'securePassword123',
      userIP: '***********'
    });
    
    console.log('Registration successful!');
  } catch (error) {
    console.error('Registration failed:', error);
  }
}
```

## 🛍️ E-commerce Essentials

### Browse Products

```typescript
// Get product categories
const categories = await sdk.product.getCategory('web', 1);

// Search products with filters
const products = await sdk.product.getSimpleProducts({
  keyword: 'laptop',
  category: 'electronics',
  priceFrom: 100000,
  priceTo: 500000,
  currentPage: 1,
  maxResult: 20
});

// Get specific product
const product = await sdk.product.getProductById('product-id-123');
```

### Manage Customers

```typescript
// Search customers
const customers = await sdk.user.searchCustomer({
  keyword: 'john',
  currentPage: 1,
  pageSize: 10
});

// Create new customer
const newCustomer = await sdk.user.createCustomerV2({
  name: 'Jane Doe',
  phone: '0123456789',
  email: '<EMAIL>'
}, 'created-by-user-id');
```

### Create Orders

```typescript
async function createOrder() {
  const orderData = {
    customer_id: 'customer-123',
    line_items: [
      {
        product_id: 'product-123',
        quantity: 2,
        input_price: 150000
      }
    ],
    shipping_address: {
      name: 'John Doe',
      phone: '0123456789',
      address: '123 Main Street',
      province_code: 'HCM',
      district_code: 'Q1',
      ward_code: 'P1'
    }
  };

  try {
    const order = await sdk.order.createOrder(
      orderData,
      'web',        // platform
      false,        // createDraft
      'user-id'     // created_by
    );
    
    console.log('Order created:', order.id);
    return order;
  } catch (error) {
    console.error('Order creation failed:', error);
  }
}
```

### Process Payments

```typescript
async function processPayment(orderId: string) {
  try {
    // Get available payment methods
    const paymentMethods = await sdk.payment.getPaymentMethodOfStoreChannel();
    
    // Generate QR payment
    const qrPayment = await sdk.payment.genQrPayment(
      orderId,
      paymentMethods[0].id
    );
    
    console.log('QR Payment generated:', qrPayment);
    return qrPayment;
  } catch (error) {
    console.error('Payment processing failed:', error);
  }
}
```

## 🔄 Complete Workflow Example

Here's a complete e-commerce workflow:

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

class EcommerceWorkflow {
  private sdk: SDK;

  constructor() {
    this.sdk = new SDK('your-org-id', 'your-store-id', 'dev');
  }

  async runCompleteFlow() {
    try {
      // 1. User Authentication
      console.log('🔐 Authenticating user...');
      const loginResponse = await this.sdk.auth.login({
        username: '<EMAIL>',
        password: 'password123'
      });
      this.sdk.setToken(loginResponse.accessToken);
      console.log('✅ User authenticated:', loginResponse.fullName);

      // 2. Browse Products
      console.log('🛍️ Browsing products...');
      const products = await this.sdk.product.getSimpleProducts({
        currentPage: 1,
        maxResult: 5
      });
      console.log('✅ Found products:', products.length);

      if (products.length === 0) {
        console.log('❌ No products available');
        return;
      }

      // 3. Create Order
      console.log('📦 Creating order...');
      const order = await this.sdk.order.createOrder({
        customer_id: loginResponse.partyId,
        line_items: [{
          product_id: products[0].id,
          quantity: 1,
          input_price: products[0].price
        }]
      }, 'web', false, loginResponse.partyId);
      console.log('✅ Order created:', order.id);

      // 4. Process Payment
      console.log('💳 Processing payment...');
      const paymentMethods = await this.sdk.payment.getPaymentMethodOfStoreChannel();
      const qrPayment = await this.sdk.payment.genQrPayment(
        order.id,
        paymentMethods[0].id
      );
      console.log('✅ Payment QR generated');

      // 5. Track Order
      console.log('📋 Tracking order...');
      const orderDetails = await this.sdk.order.getOrderDetail(order.id);
      console.log('✅ Order status:', orderDetails.status);

      console.log('🎉 Complete workflow finished successfully!');
      
    } catch (error) {
      console.error('❌ Workflow failed:', error);
    }
  }
}

// Run the workflow
const workflow = new EcommerceWorkflow();
workflow.runCompleteFlow();
```

## 🛠️ Error Handling

Always wrap your API calls in try-catch blocks:

```typescript
async function safeApiCall() {
  try {
    const result = await sdk.product.getProductById('product-id');
    return result;
  } catch (error) {
    // Handle different types of errors
    if (error.response) {
      // REST API error
      console.error('API Error:', error.response.status, error.response.data);
    } else if (error.graphQLErrors) {
      // GraphQL error
      console.error('GraphQL Errors:', error.graphQLErrors);
    } else {
      // Network or other error
      console.error('Network Error:', error.message);
    }
    
    // Return fallback or re-throw
    throw error;
  }
}
```

## 🎯 Common Patterns

### Pagination

```typescript
async function getAllProducts() {
  let currentPage = 1;
  const maxResult = 50;
  let allProducts = [];
  
  while (true) {
    const products = await sdk.product.getSimpleProducts({
      currentPage,
      maxResult
    });
    
    if (products.length === 0) break;
    
    allProducts.push(...products);
    currentPage++;
  }
  
  return allProducts;
}
```

### Retry Logic

```typescript
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
  throw new Error('Max retries exceeded');
}

// Usage
const product = await withRetry(() => 
  sdk.product.getProductById('product-id')
);
```

## 📚 Next Steps

Now that you've got the basics down, explore these topics:

### Core Services
- 🔐 [Authentication Service](../services/auth.md) - Advanced auth patterns
- 🛍️ [Product Service](../services/product.md) - Product management
- 📦 [Order Service](../services/order.md) - Order processing
- 👥 [User Service](../services/user.md) - Customer management
- 💳 [Payment Service](../services/payment.md) - Payment integration

### Advanced Topics
- 🔧 [Configuration](./configuration.md) - Environment setup
- 🚨 [Error Handling](../advanced/error-handling.md) - Robust error handling
- 🧪 [Testing](../advanced/testing.md) - Testing your integration
- ⚡ [Performance](../advanced/performance.md) - Optimization tips

### Examples
- 🛒 [E-commerce Flow](../examples/ecommerce-flow.md) - Complete examples
- 🔐 [Auth Patterns](../examples/auth-patterns.md) - Authentication examples
- 📦 [Order Processing](../examples/order-processing.md) - Order workflows

---

**Questions?** Check out the [API Reference](../api/README.md) or [open an issue](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues).
