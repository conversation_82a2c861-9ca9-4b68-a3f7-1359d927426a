import { gql } from "graphql-tag";

export const UPDATE_PRODUCT_TITLE = gql`
  mutation UpdateProductTitle(
    $productId: String!
    $title: String!
    $updatedBy: String!
  ) {
    updateProductTitle(
      productId: $productId
      title: $title
      updatedBy: $updatedBy
    ) {
      status
      message
    }
  }
`;
export const UPDATE_PRICE = gql`
  mutation UpdatePrice(
    $productId: String!
    $storeId: String!
    $price: BigDecimal!
    $updatedBy: String!
  ) {
    updatePrice(
      productId: $productId
      storeId: $storeId
      price: $price
      updatedBy: $updatedBy
    ) {
      status
      message
    }
  }
`;
export const UPDATE_PRICE_PROMOTION = gql`
  mutation UpdatePricePromotion(
    $productId: String!
    $storeId: String!
    $price: BigDecimal!
    $updatedBy: String!
  ) {
    updatePricePromotion(
      productId: $productId
      storeId: $storeId
      price: $price
      updatedBy: $updatedBy
    ) {
      status
      message
    }
  }
`;
export const UPDATE_CATEGORY = gql`
  mutation UpdateCategory(
    $productId: String!
    $categoryId: String!
    $updatedBy: String!
  ) {
    updateCategory(
      productId: $productId
      categoryId: $categoryId
      updatedBy: $updatedBy
    ) {
      status
      message
    }
  }
`;
export const UPDATE_SHORT_DESCRIPTION = gql`
  mutation UpdateShortDescription(
    $productId: String!
    $shortDescription: String!
    $updatedBy: String!
  ) {
    updateShortDescription(
      productId: $productId
      shortDescription: $shortDescription
      updatedBy: $updatedBy
    ) {
      status
      message
    }
  }
`;
export const UPDATE_UNIT = gql`
  mutation UpdateUnit(
    $productId: String!
    $unitId: String!
    $updatedBy: String!
  ) {
    updateUnit(productId: $productId, unitId: $unitId, updatedBy: $updatedBy) {
      status
      message
    }
  }
`;
export const CLEAR_ALL_CACHES = gql`
  mutation ClearAllCaches {
    clearAllCaches
  }
`;
