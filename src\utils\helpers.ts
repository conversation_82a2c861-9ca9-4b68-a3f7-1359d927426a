import CryptoJS from "crypto-js";
import { environmentEndpoints } from "../../config/config";
import { Endpoints } from "../lib/SDK";

export function createToken(environment: string): string {
  const cipherText = CryptoJS.AES.encrypt(environment, "lvs").toString();
  console.log("🚀 ~ createToken ~ cipherText:", cipherText)
  return cipherText;
}

export function decodeToken(token: string): string | null {
  try {
    const bytes = CryptoJS.AES.decrypt(token, "lvs");
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    return decryptedData;
  } catch (error) {
    console.log("Invalid token");
    return null;
  }
}
export function validateStorefrontAccessToken(
  storefrontAccessToken: string
): Endpoints {
  const environment = decodeToken(storefrontAccessToken) || ""; // Handle null case by providing a default value
  console.log("🚀 ~ environment:", environment)
  const validEnvironments = {
    dev: "dev",
    live: "live",
  };

  if (!Object.keys(validEnvironments).includes(environment)) {
    throw new Error("Invalid storefrontAccessToken");
  }

  return environment == "dev"
    ? environmentEndpoints.dev
    : environmentEndpoints.live;
}
