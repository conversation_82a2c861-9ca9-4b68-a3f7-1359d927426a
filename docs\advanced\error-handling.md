# Error Handling

Comprehensive guide to handling errors in the Longvan Storefront JavaScript Client SDK, including error types, recovery strategies, and best practices.

## Table of Contents

- [Overview](#overview)
- [Error Types](#error-types)
- [Error Handling Patterns](#error-handling-patterns)
- [Retry Strategies](#retry-strategies)
- [Error Recovery](#error-recovery)
- [Logging and Monitoring](#logging-and-monitoring)
- [Best Practices](#best-practices)

## Overview

The SDK provides robust error handling mechanisms to help you build resilient applications. Understanding different error types and implementing proper error handling strategies is crucial for production applications.

## Error Types

### Network Errors

Network-related errors occur when there are connectivity issues or server problems:

```typescript
interface NetworkError extends Error {
  code: 'NETWORK_ERROR';
  status?: number;
  response?: {
    status: number;
    statusText: string;
    data?: any;
  };
  isRetryable: boolean;
}

// Example handling
async function handleNetworkError(error: NetworkError) {
  switch (error.status) {
    case 408: // Request Timeout
    case 429: // Too Many Requests
    case 502: // Bad Gateway
    case 503: // Service Unavailable
    case 504: // Gateway Timeout
      console.log('Retryable network error:', error.message);
      return { shouldRetry: true, delay: 1000 };
    
    case 400: // Bad Request
    case 401: // Unauthorized
    case 403: // Forbidden
    case 404: // Not Found
      console.log('Non-retryable network error:', error.message);
      return { shouldRetry: false };
    
    default:
      console.log('Unknown network error:', error.message);
      return { shouldRetry: false };
  }
}
```

### GraphQL Errors

GraphQL-specific errors with detailed error information:

```typescript
interface GraphQLError {
  message: string;
  locations?: Array<{
    line: number;
    column: number;
  }>;
  path?: Array<string | number>;
  extensions?: {
    code: string;
    exception?: any;
  };
}

interface GraphQLResponse {
  data?: any;
  errors?: GraphQLError[];
  extensions?: any;
}

// Example handling
function handleGraphQLErrors(response: GraphQLResponse) {
  if (response.errors) {
    response.errors.forEach(error => {
      console.error('GraphQL Error:', {
        message: error.message,
        path: error.path,
        code: error.extensions?.code
      });
      
      // Handle specific error codes
      switch (error.extensions?.code) {
        case 'UNAUTHENTICATED':
          // Redirect to login
          window.location.href = '/login';
          break;
        case 'FORBIDDEN':
          // Show access denied message
          showErrorMessage('Access denied');
          break;
        case 'VALIDATION_ERROR':
          // Show validation errors
          showValidationErrors(error.message);
          break;
        default:
          // Generic error handling
          showErrorMessage('An error occurred');
      }
    });
  }
}
```

### Validation Errors

Client-side and server-side validation errors:

```typescript
interface ValidationError extends Error {
  code: 'VALIDATION_ERROR';
  field?: string;
  value?: any;
  constraints: string[];
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Example validation error handler
class ValidationErrorHandler {
  static handleValidationErrors(errors: ValidationError[]): void {
    const errorsByField = this.groupErrorsByField(errors);
    
    Object.entries(errorsByField).forEach(([field, fieldErrors]) => {
      const errorMessages = fieldErrors.map(error => error.constraints).flat();
      this.displayFieldErrors(field, errorMessages);
    });
  }

  private static groupErrorsByField(errors: ValidationError[]): Record<string, ValidationError[]> {
    return errors.reduce((acc, error) => {
      const field = error.field || 'general';
      if (!acc[field]) acc[field] = [];
      acc[field].push(error);
      return acc;
    }, {} as Record<string, ValidationError[]>);
  }

  private static displayFieldErrors(field: string, messages: string[]): void {
    // Display errors in UI
    const fieldElement = document.querySelector(`[data-field="${field}"]`);
    if (fieldElement) {
      const errorContainer = fieldElement.querySelector('.error-messages');
      if (errorContainer) {
        errorContainer.innerHTML = messages.map(msg => `<div class="error">${msg}</div>`).join('');
      }
    }
  }
}
```

### Business Logic Errors

Application-specific errors related to business rules:

```typescript
interface BusinessError extends Error {
  code: 'BUSINESS_ERROR';
  businessCode: string;
  details?: Record<string, any>;
}

// Example business error codes
const BUSINESS_ERROR_CODES = {
  INSUFFICIENT_INVENTORY: 'INSUFFICIENT_INVENTORY',
  INVALID_DISCOUNT_CODE: 'INVALID_DISCOUNT_CODE',
  ORDER_ALREADY_PROCESSED: 'ORDER_ALREADY_PROCESSED',
  CUSTOMER_LIMIT_EXCEEDED: 'CUSTOMER_LIMIT_EXCEEDED'
} as const;

function handleBusinessError(error: BusinessError) {
  switch (error.businessCode) {
    case BUSINESS_ERROR_CODES.INSUFFICIENT_INVENTORY:
      showInventoryError(error.details);
      break;
    case BUSINESS_ERROR_CODES.INVALID_DISCOUNT_CODE:
      showDiscountError(error.details);
      break;
    case BUSINESS_ERROR_CODES.ORDER_ALREADY_PROCESSED:
      showOrderProcessedError();
      break;
    default:
      showGenericBusinessError(error.message);
  }
}
```

## Error Handling Patterns

### Try-Catch with Specific Error Types

```typescript
async function robustApiCall() {
  try {
    const result = await sdk.product.getProductById('product-123');
    return result;
  } catch (error) {
    if (error.code === 'NETWORK_ERROR') {
      return handleNetworkError(error as NetworkError);
    } else if (error.code === 'VALIDATION_ERROR') {
      return handleValidationError(error as ValidationError);
    } else if (error.code === 'BUSINESS_ERROR') {
      return handleBusinessError(error as BusinessError);
    } else {
      return handleUnknownError(error);
    }
  }
}
```

### Error Boundary Pattern

```typescript
class SDKErrorBoundary {
  private errorHandlers: Map<string, (error: Error) => void> = new Map();

  registerErrorHandler(errorType: string, handler: (error: Error) => void): void {
    this.errorHandlers.set(errorType, handler);
  }

  async executeWithErrorBoundary<T>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      this.handleError(error, context);
      return null;
    }
  }

  private handleError(error: any, context?: string): void {
    const errorType = error.code || 'UNKNOWN_ERROR';
    const handler = this.errorHandlers.get(errorType);
    
    if (handler) {
      handler(error);
    } else {
      this.handleUnknownError(error, context);
    }
  }

  private handleUnknownError(error: any, context?: string): void {
    console.error('Unknown error occurred:', {
      error: error.message,
      context,
      stack: error.stack
    });
    
    // Report to error tracking service
    this.reportError(error, context);
  }

  private reportError(error: any, context?: string): void {
    // Send to error tracking service (e.g., Sentry, Bugsnag)
    // errorTracker.captureException(error, { context });
  }
}

// Usage
const errorBoundary = new SDKErrorBoundary();

errorBoundary.registerErrorHandler('NETWORK_ERROR', (error) => {
  console.log('Network error handled:', error.message);
});

const result = await errorBoundary.executeWithErrorBoundary(
  () => sdk.product.getProductById('product-123'),
  'product-fetch'
);
```

## Retry Strategies

### Exponential Backoff

```typescript
class RetryManager {
  static async withExponentialBackoff<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 30000,
      backoffFactor = 2,
      jitter = true
    } = options;

    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on non-retryable errors
        if (!this.isRetryableError(error)) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        let delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
        
        // Add jitter to prevent thundering herd
        if (jitter) {
          delay = delay * (0.5 + Math.random() * 0.5);
        }

        console.log(`Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms`);
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  private static isRetryableError(error: any): boolean {
    // Network errors that are typically retryable
    const retryableStatusCodes = [408, 429, 502, 503, 504];
    
    if (error.status && retryableStatusCodes.includes(error.status)) {
      return true;
    }

    // Network connectivity errors
    if (error.code === 'NETWORK_ERROR' && error.isRetryable) {
      return true;
    }

    // Timeout errors
    if (error.name === 'TimeoutError') {
      return true;
    }

    return false;
  }

  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  jitter?: boolean;
}

// Usage
const product = await RetryManager.withExponentialBackoff(
  () => sdk.product.getProductById('product-123'),
  { maxRetries: 3, baseDelay: 1000 }
);
```

### Circuit Breaker Pattern

```typescript
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000,
    private monitoringPeriod: number = 10000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }

  getFailureCount(): number {
    return this.failures;
  }
}

// Usage
const circuitBreaker = new CircuitBreaker(5, 60000);

try {
  const product = await circuitBreaker.execute(
    () => sdk.product.getProductById('product-123')
  );
} catch (error) {
  console.error('Circuit breaker prevented call or operation failed:', error);
}
```

## Error Recovery

### Graceful Degradation

```typescript
class GracefulDegradationHandler {
  async getProductWithFallback(productId: string): Promise<Product | null> {
    try {
      // Try primary data source
      return await sdk.product.getProductById(productId);
    } catch (error) {
      console.warn('Primary product fetch failed, trying cache:', error.message);
      
      try {
        // Try cache
        return await this.getProductFromCache(productId);
      } catch (cacheError) {
        console.warn('Cache fetch failed, using minimal data:', cacheError.message);
        
        // Return minimal product data
        return this.getMinimalProductData(productId);
      }
    }
  }

  async getProductsWithPartialFailure(productIds: string[]): Promise<Product[]> {
    const results = await Promise.allSettled(
      productIds.map(id => sdk.product.getProductById(id))
    );

    const products: Product[] = [];
    const failures: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        products.push(result.value);
      } else {
        failures.push(productIds[index]);
        console.warn(`Failed to fetch product ${productIds[index]}:`, result.reason);
      }
    });

    if (failures.length > 0) {
      console.log(`Successfully fetched ${products.length}/${productIds.length} products`);
    }

    return products;
  }

  private async getProductFromCache(productId: string): Promise<Product | null> {
    // Implementation would check local storage, IndexedDB, or memory cache
    const cached = localStorage.getItem(`product_${productId}`);
    return cached ? JSON.parse(cached) : null;
  }

  private getMinimalProductData(productId: string): Product | null {
    // Return minimal product data for graceful degradation
    return {
      id: productId,
      title: 'Product information temporarily unavailable',
      price: 0,
      available: false,
      description: '',
      sku: '',
      categories: [],
      featuredImage: '/placeholder-image.jpg',
      subType: ''
    };
  }
}
```

### Offline Support

```typescript
class OfflineHandler {
  private isOnline = navigator.onLine;
  private pendingOperations: Array<() => Promise<any>> = [];

  constructor() {
    window.addEventListener('online', () => this.handleOnline());
    window.addEventListener('offline', () => this.handleOffline());
  }

  async executeWithOfflineSupport<T>(
    operation: () => Promise<T>,
    fallback?: () => T
  ): Promise<T> {
    if (!this.isOnline) {
      if (fallback) {
        console.log('Offline: using fallback');
        return fallback();
      } else {
        // Queue operation for when online
        this.pendingOperations.push(operation);
        throw new Error('Operation queued for when online');
      }
    }

    try {
      return await operation();
    } catch (error) {
      if (this.isNetworkError(error)) {
        this.handleOffline();
        if (fallback) {
          return fallback();
        }
      }
      throw error;
    }
  }

  private handleOnline(): void {
    console.log('Back online, processing pending operations');
    this.isOnline = true;
    
    // Process pending operations
    const operations = [...this.pendingOperations];
    this.pendingOperations = [];
    
    operations.forEach(async (operation) => {
      try {
        await operation();
      } catch (error) {
        console.error('Failed to process pending operation:', error);
      }
    });
  }

  private handleOffline(): void {
    console.log('Gone offline');
    this.isOnline = false;
  }

  private isNetworkError(error: any): boolean {
    return error.code === 'NETWORK_ERROR' || 
           error.name === 'NetworkError' ||
           !navigator.onLine;
  }
}
```

## Logging and Monitoring

### Error Logging

```typescript
class ErrorLogger {
  private static instance: ErrorLogger;
  private logs: ErrorLog[] = [];

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  logError(error: Error, context?: ErrorContext): void {
    const errorLog: ErrorLog = {
      id: this.generateId(),
      timestamp: new Date(),
      message: error.message,
      stack: error.stack,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.logs.push(errorLog);
    
    // Send to logging service
    this.sendToLoggingService(errorLog);
    
    // Keep only last 100 logs in memory
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100);
    }
  }

  getLogs(): ErrorLog[] {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private async sendToLoggingService(errorLog: ErrorLog): Promise<void> {
    try {
      // Send to external logging service
      await fetch('/api/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorLog)
      });
    } catch (error) {
      console.error('Failed to send error log:', error);
    }
  }
}

interface ErrorLog {
  id: string;
  timestamp: Date;
  message: string;
  stack?: string;
  context?: ErrorContext;
  userAgent: string;
  url: string;
}

interface ErrorContext {
  operation?: string;
  userId?: string;
  sessionId?: string;
  additionalData?: Record<string, any>;
}

// Usage
const errorLogger = ErrorLogger.getInstance();

try {
  await sdk.product.getProductById('invalid-id');
} catch (error) {
  errorLogger.logError(error as Error, {
    operation: 'getProductById',
    userId: 'user-123',
    additionalData: { productId: 'invalid-id' }
  });
}
```

## Best Practices

### 1. Error Handling Hierarchy

```typescript
// ✅ Good: Implement error handling hierarchy
class ErrorHandlingService {
  async handleSDKOperation<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      // 1. Log the error
      this.logError(error, context);
      
      // 2. Try recovery strategies
      const recovered = await this.attemptRecovery(error, operation);
      if (recovered !== null) {
        return recovered;
      }
      
      // 3. Graceful degradation
      const fallback = this.getFallbackData(context);
      if (fallback !== null) {
        return fallback;
      }
      
      // 4. User notification
      this.notifyUser(error, context);
      
      // 5. Re-throw if critical
      if (this.isCriticalError(error)) {
        throw error;
      }
      
      return null;
    }
  }

  private async attemptRecovery<T>(
    error: any,
    operation: () => Promise<T>
  ): Promise<T | null> {
    if (this.isRetryableError(error)) {
      try {
        return await RetryManager.withExponentialBackoff(operation);
      } catch (retryError) {
        console.log('Retry failed:', retryError.message);
      }
    }
    return null;
  }

  private getFallbackData(context: string): any {
    // Return cached or default data based on context
    switch (context) {
      case 'product-list':
        return this.getCachedProducts();
      case 'user-profile':
        return this.getCachedUserProfile();
      default:
        return null;
    }
  }

  private notifyUser(error: any, context: string): void {
    const userMessage = this.getUserFriendlyMessage(error, context);
    // Show notification to user
    this.showNotification(userMessage, 'error');
  }

  private getUserFriendlyMessage(error: any, context: string): string {
    if (error.code === 'NETWORK_ERROR') {
      return 'Connection problem. Please check your internet connection.';
    } else if (error.code === 'VALIDATION_ERROR') {
      return 'Please check your input and try again.';
    } else {
      return 'Something went wrong. Please try again later.';
    }
  }

  private isCriticalError(error: any): boolean {
    const criticalCodes = ['AUTHENTICATION_FAILED', 'AUTHORIZATION_FAILED'];
    return criticalCodes.includes(error.code);
  }

  // Placeholder methods
  private logError(error: any, context: string): void {
    ErrorLogger.getInstance().logError(error, { operation: context });
  }

  private isRetryableError(error: any): boolean {
    return RetryManager['isRetryableError'](error);
  }

  private getCachedProducts(): any { return null; }
  private getCachedUserProfile(): any { return null; }
  private showNotification(message: string, type: string): void {
    console.log(`${type.toUpperCase()}: ${message}`);
  }
}
```

### 2. Error Boundaries for React

```typescript
// ✅ Good: React error boundary for SDK operations
import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class SDKErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('SDK Error Boundary caught an error:', error, errorInfo);
    
    // Log to error tracking service
    ErrorLogger.getInstance().logError(error, {
      operation: 'react-error-boundary',
      additionalData: errorInfo
    });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Usage
function App() {
  return (
    <SDKErrorBoundary
      fallback={<div>Error loading products</div>}
      onError={(error) => console.log('Product error:', error)}
    >
      <ProductList />
    </SDKErrorBoundary>
  );
}
```

---

**Next Steps:**
- [TypeScript Support](./typescript.md) - Advanced TypeScript patterns
- [Testing](./testing.md) - Testing strategies and patterns
- [Performance](./performance.md) - Performance optimization techniques
