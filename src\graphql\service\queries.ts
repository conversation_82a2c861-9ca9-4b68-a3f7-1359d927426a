import { gql } from "graphql-tag";

export const GET_SERVICE_BY_ID = gql`
  query GetServiceById($partnerId: String!, $serviceId: String!) {
    getServiceById(partnerId: $partnerId, serviceId: $serviceId) {
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partnerId
      serviceName
      serviceId
      type
      typeName
      orderId
      orderLineItemId
      status
      ownerId
      ownerName
      ownerEmail
      ownerPhone
      saleName
      salePartyId
      startDate
      endDate
      location
      description
      serviceType
      actionResult
      attrs
    }
  }
`;
// GET_SERVICE_BY_TYPE
export const GET_SERVICE_BY_TYPE = gql`
  query GetServiceByType($partnerId: String!, $type: String!) {
    getServiceByType(partnerId: $partnerId, type: $type) {
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partnerId
      serviceName
      serviceId
      type
      typeName
      orderId
      status
      ownerId
      ownerName
      ownerEmail
      ownerPhone
      saleName
      salePartyId
      startDate
      endDate
      version
      location
      description
      serviceType
      actionResult
    }
  }
`;

// query GetServiceByOwnerId {
//   getServiceByOwnerId(partnerId: "LONGVAN", ownerId: "20.52731") {
//       createdStamp
//       updatedStamp
//       updatedBy
//       createdBy
//       partnerId
//       serviceName
//       serviceId
//       type
//       typeName
//       orderId
//       orderLineItemId
//       orderItemTermId
//       status
//       ownerId
//       ownerName
//       ownerEmail
//       ownerPhone
//       saleName
//       salePartyId
//       startDate
//       endDate
//       version
//       location
//       description
//       username
//       password
//       urlPrivate
//       urlPublic
//       serviceType
//       actionResult
//       attrs
//   }
// }

export const GET_SERVICE_BY_OWNER_ID = gql`
  query GetServiceByOwnerId($partnerId: String!, $ownerId: String!) {
    getServiceByOwnerId(partnerId: $partnerId, ownerId: $ownerId) {
      createdStamp
      updatedStamp
      updatedBy
      createdBy
      partnerId
      serviceName
      serviceId
      type
      typeName
      orderId
      orderLineItemId
      status
      ownerId
      ownerName
      ownerEmail
      ownerPhone
      saleName
      salePartyId
      startDate
      endDate
      location
      description
      serviceType
      actionResult
      attrs
    }
  }
`;
export const GET_SERVICE_ACTION = gql`
  query GetServiceActions(
    $serviceId: String!
    $actionType: String
    $updatedBy: String
  ) {
    getServiceActions(
      serviceId: $serviceId
      actionType: $actionType
      updatedBy: $updatedBy
    ) {
      id
      serviceId
      actionType
      description
      status
      processStatus
      createdBy
      createdStamp
      updatedBy
      updatedStamp
      attributes
    }
  }
`;

export const GET_SERVICE_TICKETS = gql`
  query GetServiceTickets($serviceId: String!) {
    getServiceTickets(serviceId: $serviceId) {
      id
      serviceId
      description
      status
      processStatus
      createdBy
      createdStamp
      updatedBy
      updatedStamp
    }
  }
`;

export const GET_ACTION_ATTRIBUTE = gql`
  query GetActionAttribute($actionId: String!, $attributeName: String!) {
    getActionAttribute(actionId: $actionId, attributeName: $attributeName)
  }
`;
