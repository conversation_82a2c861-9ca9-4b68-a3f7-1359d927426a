# Installation & Setup

This guide will help you install and set up the Longvan Storefront JavaScript Client SDK in your project.

## Prerequisites

Before installing the SDK, ensure you have:

- **Node.js** version 14 or higher
- **npm** or **yarn** package manager
- A Longvan platform account with valid credentials
- Organization ID and Store ID from your Longvan dashboard

## Installation

### Using npm

```bash
npm install @longvansoftware/storefront-js-client
```

### Using yarn

```bash
yarn add @longvansoftware/storefront-js-client
```

### Using pnpm

```bash
pnpm add @longvansoftware/storefront-js-client
```

## Verification

After installation, verify the SDK is properly installed:

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

console.log('SDK imported successfully!');
```

## TypeScript Setup

The SDK is built with TypeScript and includes type definitions. No additional type packages are needed.

### tsconfig.json Configuration

Ensure your `tsconfig.json` includes these settings:

```json
{
  "compilerOptions": {
    "target": "ES2018",
    "module": "commonjs",
    "lib": ["ES2018", "DOM"],
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

## Environment Setup

### Development Environment

Create a `.env` file in your project root:

```env
LONGVAN_ORG_ID=your-organization-id
LONGVAN_STORE_ID=your-store-id
LONGVAN_ENVIRONMENT=dev
LONGVAN_ACCESS_TOKEN=your-access-token
```

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `LONGVAN_ORG_ID` | Your organization identifier | ✅ |
| `LONGVAN_STORE_ID` | Your store identifier | ✅ |
| `LONGVAN_ENVIRONMENT` | Environment (`dev` or `live`) | ✅ |
| `LONGVAN_ACCESS_TOKEN` | Authentication token | ⚠️ (for authenticated requests) |

## Basic Setup

### 1. Initialize the SDK

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

const sdk = new SDK(
  process.env.LONGVAN_ORG_ID!,
  process.env.LONGVAN_STORE_ID!,
  process.env.LONGVAN_ENVIRONMENT as 'dev' | 'live'
);
```

### 2. Set Authentication Token (if needed)

```typescript
// Set token for authenticated requests
if (process.env.LONGVAN_ACCESS_TOKEN) {
  sdk.setToken(process.env.LONGVAN_ACCESS_TOKEN);
}
```

### 3. Test the Connection

```typescript
async function testConnection() {
  try {
    // Test with a simple product query
    const products = await sdk.product.getSimpleProducts({
      currentPage: 1,
      maxResult: 1
    });
    
    console.log('✅ SDK connection successful!');
    console.log('Sample product:', products[0]?.title || 'No products found');
  } catch (error) {
    console.error('❌ SDK connection failed:', error);
  }
}

testConnection();
```

## Framework-Specific Setup

### React/Next.js

```typescript
// hooks/useLongvanSDK.ts
import { SDK } from '@longvansoftware/storefront-js-client';
import { useMemo } from 'react';

export function useLongvanSDK() {
  const sdk = useMemo(() => {
    return new SDK(
      process.env.NEXT_PUBLIC_LONGVAN_ORG_ID!,
      process.env.NEXT_PUBLIC_LONGVAN_STORE_ID!,
      process.env.NEXT_PUBLIC_LONGVAN_ENVIRONMENT as 'dev' | 'live'
    );
  }, []);

  return sdk;
}
```

### Vue.js

```typescript
// plugins/longvan-sdk.ts
import { SDK } from '@longvansoftware/storefront-js-client';

export default defineNuxtPlugin(() => {
  const sdk = new SDK(
    process.env.LONGVAN_ORG_ID!,
    process.env.LONGVAN_STORE_ID!,
    process.env.LONGVAN_ENVIRONMENT as 'dev' | 'live'
  );

  return {
    provide: {
      longvanSDK: sdk
    }
  };
});
```

### Express.js/Node.js

```typescript
// services/longvan.ts
import { SDK } from '@longvansoftware/storefront-js-client';

class LongvanService {
  private sdk: SDK;

  constructor() {
    this.sdk = new SDK(
      process.env.LONGVAN_ORG_ID!,
      process.env.LONGVAN_STORE_ID!,
      process.env.LONGVAN_ENVIRONMENT as 'dev' | 'live'
    );
  }

  async authenticateUser(token: string) {
    this.sdk.setToken(token);
  }

  getSDK() {
    return this.sdk;
  }
}

export const longvanService = new LongvanService();
```

## Troubleshooting

### Common Issues

#### 1. Module Not Found Error

```bash
Error: Cannot find module '@longvansoftware/storefront-js-client'
```

**Solution**: Ensure the package is properly installed:
```bash
npm install @longvansoftware/storefront-js-client
```

#### 2. TypeScript Compilation Errors

```bash
Error: Cannot find type definitions
```

**Solution**: The SDK includes TypeScript definitions. Ensure your `tsconfig.json` is properly configured.

#### 3. Network/CORS Issues

```bash
Error: Network request failed
```

**Solution**: Check your environment configuration and ensure you're using the correct endpoints.

#### 4. Authentication Errors

```bash
Error: Unauthorized (401)
```

**Solution**: Verify your access token and ensure it's properly set:
```typescript
sdk.setToken('your-valid-token');
```

### Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](../advanced/troubleshooting.md)
2. Review the [API Reference](../api/README.md)
3. Open an issue on [GitLab](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues)

## Next Steps

Now that you have the SDK installed and configured:

1. 📖 Read the [Quick Start Guide](./quick-start.md)
2. 🔐 Set up [Authentication](./authentication.md)
3. 🛍️ Explore [Product Management](../services/product.md)
4. 📦 Learn about [Order Processing](../services/order.md)

---

**Need help?** Check out our [Quick Start Guide](./quick-start.md) or browse the [API Reference](../api/README.md).
