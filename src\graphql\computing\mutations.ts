import { gql } from "graphql-tag";

export const RESTARTVM = gql`
  mutation Restart($computingId: String!, $actor: String!) {
    restart(computingId: $computingId, actor: $actor)
  }
`;

export const UPDATE_DESCRIPTION_PORTNAT = gql`
  mutation UpdateDescriptionPortNat(
    $portNatId: String!
    $description: String!
    $updateBy: String
  ) {
    updateDescriptionPortNat(
      portNatId: $portNatId
      description: $description
      updateBy: $updateBy
    )
  }
`;

export const CREATE_PORT_NAT = gql`
  mutation CreatePortNat(
    $portNatId: String!
    $translatedPort: String!
    $createBy: String
  ) {
    createPortNat(
      portNatId: $portNatId
      translatedPort: $translatedPort
      createBy: $createBy
    )
  }
`;

export const UPDATE_PORT_NAT = gql`
  mutation UpdatePortNat(
    $portNatId: String!
    $translatedPort: String!
    $updateBy: String
  ) {
    updatePortNat(
      portNatId: $portNatId
      translatedPort: $translatedPort
      updateBy: $updateBy
    )
  }
`;

export const REMOVE_PORT_NAT = gql`
  mutation RemovePortNat($portNatId: String!, $updateBy: String) {
    removePortNat(portNatId: $portNatId, updateBy: $updateBy)
  }
`;

export const POWER_ON = gql`
  mutation PowerOn($computingId: String!, $actor: String!) {
    powerOn(computingId: $computingId, actor: $actor)
  }
`;

export const POWER_OFF = gql`
  mutation PowerOff($computingId: String!, $actor: String!) {
    powerOff(computingId: $computingId, actor: $actor)
  }
`;

export const CREATE_SNAP_SHOT = gql`
  mutation CreateSnapshot(
    $computingId: String!
    $snapshotName: String!
    $createBy: String
  ) {
    createSnapshot(
      computingId: $computingId
      snapshotName: $snapshotName
      createBy: $createBy
    ) {
      id
      computingId
      snapshotId
      name
      status
      createdStamp
      endDate
    }
  }
`;

export const ROLLBACK_SNAPSHOT = gql`
  mutation RollbackSnapshot(
    $computingId: String!
    $snapshotId: String!
    $createBy: String
  ) {
    rollbackSnapshot(
      computingId: $computingId
      snapshotId: $snapshotId
      createBy: $createBy
    )
  }
`;

export const DELETE_SNAPSHOT = gql`
  mutation DeleteSnapshot(
    $computingId: String!
    $snapshotId: String!
    $updateBy: String
  ) {
    deleteSnapshot(
      computingId: $computingId
      snapshotId: $snapshotId
      updateBy: $updateBy
    )
  }
`;
