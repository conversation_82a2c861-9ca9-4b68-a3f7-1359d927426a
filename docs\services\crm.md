# CRM Service

The CRM Service manages customer relationships, support tickets, sales opportunities, work efforts, and customer communication tracking.

## Table of Contents

- [Overview](#overview)
- [Work Effort Management](#work-effort-management)
- [Ticket Management](#ticket-management)
- [Opportunity Management](#opportunity-management)
- [Comment System](#comment-system)
- [Attachment Management](#attachment-management)
- [Best Practices](#best-practices)

## Overview

The CRM Service provides comprehensive customer relationship management functionality:

- **Work Effort Management**: Create and manage work efforts for various business processes
- **Ticket Management**: Handle customer support tickets and service requests
- **Opportunity Management**: Track sales opportunities and potential deals
- **Comment System**: Add and manage comments on work efforts and tickets
- **Attachment Management**: Handle file attachments for work efforts and tickets
- **Status Tracking**: Monitor progress and status changes across all CRM entities

## Work Effort Management

### Create Work Effort

Create a new work effort for tracking business activities:

```typescript
async function createWorkEffort() {
  try {
    const workEffort = await sdk.crm.createWorkEffort(
      'created-by-user-id',           // createdBy
      'Customer Support Request',      // name
      'Customer needs help with product setup and configuration', // description
      'work-effort-type-id',          // workEffortTypeId
      'web',                          // source
      {                               // attributes
        priority: 'high',
        category: 'technical-support',
        estimatedHours: 2
      },
      {                               // addAttachmentRequest
        files: []
      },
      'parent-work-effort-id'         // parentId (optional)
    );
    
    console.log('Work effort created:', {
      id: workEffort.id,
      name: workEffort.name,
      status: workEffort.status,
      createdDate: workEffort.createdDate
    });
    
    return workEffort;
  } catch (error) {
    console.error('Work effort creation failed:', error);
    throw error;
  }
}
```

### Get Work Effort Details

Retrieve detailed information about a work effort:

```typescript
async function getWorkEffortDetails(workEffortId: string) {
  try {
    const workEffort = await sdk.crm.getWorkEffortById(workEffortId);
    
    console.log('Work effort details:', {
      id: workEffort.id,
      name: workEffort.name,
      description: workEffort.description,
      status: workEffort.status,
      assignedTo: workEffort.assignedTo,
      priority: workEffort.priority,
      createdDate: workEffort.createdDate,
      lastUpdated: workEffort.lastUpdated
    });
    
    return workEffort;
  } catch (error) {
    console.error('Failed to fetch work effort details:', error);
    throw error;
  }
}
```

### Update Work Effort Status

Update the status of a work effort:

```typescript
async function updateWorkEffortStatus(
  workEffortId: string, 
  newStatus: string, 
  updatedBy: string
) {
  try {
    await sdk.crm.updateWorkEffortStatus(workEffortId, newStatus, updatedBy);
    
    console.log(`Work effort ${workEffortId} status updated to ${newStatus}`);
  } catch (error) {
    console.error('Failed to update work effort status:', error);
    throw error;
  }
}

// Common work effort statuses
const WORK_EFFORT_STATUS = {
  CREATED: 'CREATED',
  IN_PROGRESS: 'IN_PROGRESS',
  ON_HOLD: 'ON_HOLD',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
} as const;
```

### Work Effort Data Structure

```typescript
interface WorkEffort {
  id: string;
  name: string;
  description: string;
  workEffortTypeId: string;
  status: string;
  priority: string;
  source: string;
  assignedTo?: string;
  createdBy: string;
  createdDate: Date;
  lastUpdated: Date;
  estimatedHours?: number;
  actualHours?: number;
  parentId?: string;
  attributes?: Record<string, any>;
}

interface CreateWorkEffortRequest {
  name: string;
  description: string;
  workEffortTypeId: string;
  source: string;
  attributes?: Record<string, any>;
  parentId?: string;
}
```

## Ticket Management

### Create Ticket

Create a new support ticket:

```typescript
async function createTicket() {
  const ticketData = {
    title: 'Product Installation Issue',
    description: 'Customer unable to install product on Windows 11',
    customerId: 'customer-123',
    priority: 'HIGH',
    category: 'TECHNICAL_SUPPORT',
    source: 'web',
    assignedTo: 'support-agent-456'
  };

  try {
    const ticket = await sdk.crm.addTicket(ticketData);
    
    console.log('Ticket created:', {
      id: ticket.id,
      ticketNumber: ticket.ticketNumber,
      title: ticket.title,
      status: ticket.status
    });
    
    return ticket;
  } catch (error) {
    console.error('Ticket creation failed:', error);
    throw error;
  }
}
```

### Get Ticket List

Retrieve tickets with filtering options:

```typescript
async function getTickets() {
  try {
    const tickets = await sdk.crm.getListTicket(
      'work-effort-type-id',  // workEffortTypeId
      1,                      // currentPage
      20,                     // pageSize
      'OPEN'                  // status (optional)
    );
    
    console.log(`Found ${tickets.length} tickets`);
    
    tickets.forEach(ticket => {
      console.log(`Ticket ${ticket.ticketNumber}: ${ticket.title} - ${ticket.status}`);
    });
    
    return tickets;
  } catch (error) {
    console.error('Failed to fetch tickets:', error);
    throw error;
  }
}
```

### Get Ticket Details

Retrieve detailed information about a specific ticket:

```typescript
async function getTicketDetails(ticketId: string) {
  try {
    const ticket = await sdk.crm.getTicketById(ticketId);
    
    console.log('Ticket details:', {
      id: ticket.id,
      ticketNumber: ticket.ticketNumber,
      title: ticket.title,
      description: ticket.description,
      status: ticket.status,
      priority: ticket.priority,
      customerId: ticket.customerId,
      assignedTo: ticket.assignedTo,
      createdDate: ticket.createdDate,
      lastUpdated: ticket.lastUpdated
    });
    
    return ticket;
  } catch (error) {
    console.error('Failed to fetch ticket details:', error);
    throw error;
  }
}
```

### Ticket Data Structure

```typescript
interface Ticket {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: string;
  customerId: string;
  assignedTo?: string;
  createdBy: string;
  createdDate: Date;
  lastUpdated: Date;
  resolvedDate?: Date;
  source: string;
}

type TicketStatus = 
  | 'OPEN'
  | 'IN_PROGRESS'
  | 'PENDING_CUSTOMER'
  | 'RESOLVED'
  | 'CLOSED'
  | 'CANCELLED';

type TicketPriority = 
  | 'LOW'
  | 'MEDIUM'
  | 'HIGH'
  | 'URGENT';
```

## Opportunity Management

### Add Opportunity

Create a new sales opportunity:

```typescript
async function addOpportunity(performerId: string) {
  const opportunityData = {
    title: 'Enterprise Software License',
    description: 'Potential customer interested in enterprise package for 100 users',
    customerId: 'customer-789',
    estimatedValue: 50000000, // 50 million VND
    probability: 75,           // 75% chance
    expectedCloseDate: '2024-06-30',
    stage: 'PROPOSAL',
    source: 'website_inquiry'
  };

  try {
    const opportunity = await sdk.crm.addOpportunity(performerId, opportunityData);
    
    console.log('Opportunity created:', {
      id: opportunity.id,
      title: opportunity.title,
      estimatedValue: opportunity.estimatedValue,
      stage: opportunity.stage
    });
    
    return opportunity;
  } catch (error) {
    console.error('Opportunity creation failed:', error);
    throw error;
  }
}
```

### Get Opportunity List

Retrieve opportunities for a performer:

```typescript
async function getOpportunities(performerId: string) {
  try {
    const opportunities = await sdk.crm.getListOpportunity(
      performerId,
      1,    // currentPage
      20    // pageSize
    );
    
    console.log(`Found ${opportunities.length} opportunities`);
    
    const totalValue = opportunities.reduce((sum, opp) => sum + opp.estimatedValue, 0);
    console.log(`Total estimated value: ${totalValue.toLocaleString()} VND`);
    
    return opportunities;
  } catch (error) {
    console.error('Failed to fetch opportunities:', error);
    throw error;
  }
}
```

### Opportunity Data Structure

```typescript
interface Opportunity {
  id: string;
  title: string;
  description: string;
  customerId: string;
  performerId: string;
  estimatedValue: number;
  probability: number;        // 0-100
  expectedCloseDate: Date;
  actualCloseDate?: Date;
  stage: OpportunityStage;
  status: OpportunityStatus;
  source: string;
  createdDate: Date;
  lastUpdated: Date;
}

type OpportunityStage = 
  | 'LEAD'
  | 'QUALIFIED'
  | 'PROPOSAL'
  | 'NEGOTIATION'
  | 'CLOSED_WON'
  | 'CLOSED_LOST';

type OpportunityStatus = 
  | 'ACTIVE'
  | 'INACTIVE'
  | 'COMPLETED';
```

## Comment System

### Add Comment

Add a comment to a work effort or ticket:

```typescript
async function addComment(workEffortId: string, commentText: string, createdBy: string) {
  try {
    const comment = await sdk.crm.addComment(workEffortId, commentText, createdBy);
    
    console.log('Comment added:', {
      id: comment.id,
      text: comment.text,
      createdBy: comment.createdBy,
      createdDate: comment.createdDate
    });
    
    return comment;
  } catch (error) {
    console.error('Failed to add comment:', error);
    throw error;
  }
}
```

### Get Comments

Retrieve comments for a work effort:

```typescript
async function getComments(workEffortId: string) {
  try {
    const comments = await sdk.crm.getListComment(
      workEffortId,
      1,    // currentPage
      50    // pageSize
    );
    
    console.log(`Found ${comments.length} comments`);
    
    comments.forEach(comment => {
      console.log(`${comment.createdBy} (${comment.createdDate}): ${comment.text}`);
    });
    
    return comments;
  } catch (error) {
    console.error('Failed to fetch comments:', error);
    throw error;
  }
}
```

### Comment Data Structure

```typescript
interface Comment {
  id: string;
  workEffortId: string;
  text: string;
  createdBy: string;
  createdDate: Date;
  updatedDate?: Date;
  isInternal: boolean;       // Internal comment vs customer-visible
}
```

## Attachment Management

### Add Attachment

Add file attachments to work efforts:

```typescript
async function addAttachment(workEffortId: string) {
  const attachmentRequest = {
    files: [
      {
        fileName: 'screenshot.png',
        fileType: 'image/png',
        fileSize: 1024000,
        fileUrl: 'https://example.com/files/screenshot.png',
        description: 'Error screenshot from customer'
      },
      {
        fileName: 'log_file.txt',
        fileType: 'text/plain',
        fileSize: 5120,
        fileUrl: 'https://example.com/files/log_file.txt',
        description: 'Application log file'
      }
    ]
  };

  try {
    const result = await sdk.crm.addAttachmentForWorkEffort(workEffortId, attachmentRequest);
    
    console.log('Attachments added:', result);
    return result;
  } catch (error) {
    console.error('Failed to add attachments:', error);
    throw error;
  }
}
```

### Attachment Data Structure

```typescript
interface AttachmentRequest {
  files: AttachmentFile[];
}

interface AttachmentFile {
  fileName: string;
  fileType: string;
  fileSize: number;
  fileUrl: string;
  description?: string;
}

interface Attachment {
  id: string;
  workEffortId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  fileUrl: string;
  description?: string;
  uploadedBy: string;
  uploadedDate: Date;
}
```

## Best Practices

### 1. CRM Workflow Management

```typescript
// ✅ Good: Implement complete CRM workflow
class CRMWorkflowManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async createCustomerSupportCase(
    customerId: string, 
    issue: string, 
    priority: TicketPriority = 'MEDIUM'
  ) {
    try {
      // 1. Create work effort
      const workEffort = await this.sdk.crm.createWorkEffort(
        'system',
        `Support Case for Customer ${customerId}`,
        issue,
        'support-case-type',
        'web',
        { priority, customerId },
        { files: [] }
      );

      // 2. Create ticket
      const ticket = await this.sdk.crm.addTicket({
        title: `Support Case #${workEffort.id}`,
        description: issue,
        customerId,
        priority,
        workEffortId: workEffort.id
      });

      // 3. Add initial comment
      await this.sdk.crm.addComment(
        workEffort.id,
        'Support case created automatically from customer request',
        'system'
      );

      return { workEffort, ticket };
    } catch (error) {
      console.error('Failed to create support case:', error);
      throw error;
    }
  }

  async escalateTicket(ticketId: string, reason: string, escalatedBy: string) {
    try {
      // Update ticket status
      await this.sdk.crm.updateWorkEffortStatus(ticketId, 'ESCALATED', escalatedBy);

      // Add escalation comment
      await this.sdk.crm.addComment(
        ticketId,
        `Ticket escalated. Reason: ${reason}`,
        escalatedBy
      );

      console.log(`Ticket ${ticketId} escalated successfully`);
    } catch (error) {
      console.error('Failed to escalate ticket:', error);
      throw error;
    }
  }
}
```

### 2. Opportunity Pipeline Management

```typescript
// ✅ Good: Track opportunity pipeline
class OpportunityPipelineManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getOpportunityPipeline(performerId: string) {
    try {
      const opportunities = await this.sdk.crm.getListOpportunity(performerId, 1, 100);
      
      const pipeline = {
        totalOpportunities: opportunities.length,
        totalValue: opportunities.reduce((sum, opp) => sum + opp.estimatedValue, 0),
        byStage: this.groupOpportunitiesByStage(opportunities),
        weightedValue: this.calculateWeightedValue(opportunities)
      };

      return pipeline;
    } catch (error) {
      console.error('Failed to get opportunity pipeline:', error);
      throw error;
    }
  }

  private groupOpportunitiesByStage(opportunities: Opportunity[]) {
    return opportunities.reduce((acc, opp) => {
      if (!acc[opp.stage]) {
        acc[opp.stage] = { count: 0, value: 0 };
      }
      acc[opp.stage].count++;
      acc[opp.stage].value += opp.estimatedValue;
      return acc;
    }, {} as Record<string, { count: number; value: number }>);
  }

  private calculateWeightedValue(opportunities: Opportunity[]): number {
    return opportunities.reduce((sum, opp) => {
      return sum + (opp.estimatedValue * opp.probability / 100);
    }, 0);
  }
}
```

### 3. Comment and Communication Tracking

```typescript
// ✅ Good: Comprehensive communication tracking
class CommunicationTracker {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async addTimestampedComment(
    workEffortId: string, 
    comment: string, 
    createdBy: string,
    isInternal: boolean = false
  ) {
    const timestampedComment = `[${new Date().toISOString()}] ${comment}`;
    
    if (isInternal) {
      timestampedComment = `[INTERNAL] ${timestampedComment}`;
    }

    return await this.sdk.crm.addComment(workEffortId, timestampedComment, createdBy);
  }

  async getCommentHistory(workEffortId: string) {
    try {
      const comments = await this.sdk.crm.getListComment(workEffortId, 1, 100);
      
      return comments.sort((a, b) => 
        new Date(a.createdDate).getTime() - new Date(b.createdDate).getTime()
      );
    } catch (error) {
      console.error('Failed to get comment history:', error);
      throw error;
    }
  }

  async addStatusChangeComment(
    workEffortId: string, 
    oldStatus: string, 
    newStatus: string, 
    changedBy: string
  ) {
    const comment = `Status changed from ${oldStatus} to ${newStatus}`;
    return await this.addTimestampedComment(workEffortId, comment, changedBy, true);
  }
}
```

### 4. Error Handling and Validation

```typescript
// ✅ Good: Validate CRM data
class CRMValidator {
  static validateWorkEffort(data: CreateWorkEffortRequest): string[] {
    const errors: string[] = [];
    
    if (!data.name || data.name.trim().length < 3) {
      errors.push('Work effort name must be at least 3 characters');
    }
    
    if (!data.description || data.description.trim().length < 10) {
      errors.push('Work effort description must be at least 10 characters');
    }
    
    if (!data.workEffortTypeId) {
      errors.push('Work effort type is required');
    }
    
    return errors;
  }

  static validateOpportunity(data: any): string[] {
    const errors: string[] = [];
    
    if (!data.title || data.title.trim().length < 5) {
      errors.push('Opportunity title must be at least 5 characters');
    }
    
    if (!data.estimatedValue || data.estimatedValue <= 0) {
      errors.push('Estimated value must be greater than 0');
    }
    
    if (!data.probability || data.probability < 0 || data.probability > 100) {
      errors.push('Probability must be between 0 and 100');
    }
    
    return errors;
  }
}
```

---

**Next Steps:**
- [Warehouse Service](./warehouse.md) - Inventory management
- [Computing Service](./computing.md) - Cloud computing services
- [Campaign Service](./campaign.md) - Marketing campaigns
