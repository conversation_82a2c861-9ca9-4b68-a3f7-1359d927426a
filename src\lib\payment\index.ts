import {
  CREATE_PAYMENT_ORDER_MUTATION,
  REQUEST_UNPUBLISH_VAT_INVOICE,
  REQUEST_PUBLISH_VAT_INVOICE,
  UPDATE_INVOICE_ITEM,
} from "../../graphql/payment/mutations";
import {
  GEN_QR_PAYMENT,
  GET_PAYMENT_METHOD,
  GET_PAYMENT_METHOD_OF_STORE_CHANNEL,
  GET_INVOICE_DETAIL,
  GET_INVOICES_OF_ORDER,
  VIEW_PUBLISHED_INVOICE,
  GET_INVOICE_ITEM_OF_INVOICE,
} from "../../graphql/payment/queries";
import { Service } from "../serviceSDK";
import { VatInvoiceRequestDTO } from "../../types/invoice";
export class PaymentService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async getPaymentMethod() {
    const query = GET_PAYMENT_METHOD;
    const variables = {
      orgId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getPaymentMethod;
    } catch (error) {
      console.log(`Error fetching get payment method: ${error}`);
      throw error;
    }
  }
  async createPaymentOrder(paymentOrderData: any) {
    const data = {
      orgId: this.orgId,
      storeId: this.storeId,
      ...paymentOrderData,
    };
    try {
      const response = await this.graphqlMutationV3(
        CREATE_PAYMENT_ORDER_MUTATION,
        data
      );
      return response.createPaymentOrder;
    } catch (error) {
      console.log(`Error in createPaymentOrder: ${error}`);
      throw error;
    }
  }
  async genQRPayment(orderId: any, totalAmount: any) {
    const query = GEN_QR_PAYMENT;
    const variables = {
      orgId: this.orgId,
      orderId,
      storeId: this.storeId,
      totalAmount,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.genQRPayment;
    } catch (error) {
      console.log(`Error fetching get genQRPayment method: ${error}`);
      throw error;
    }
  }
  async getPaymentMethodOfStoreChannel() {
    const query = GET_PAYMENT_METHOD_OF_STORE_CHANNEL;
    const variables = {
      orgId: this.orgId,
      storeChannelId: this.storeId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getPaymentMethodOfStoreChannel;
    } catch (error) {
      throw error;
    }
  }
  async getInvoiceDetail(invoiceId: string) {
    const query = GET_INVOICE_DETAIL;
    const variables = {
      invoiceId: invoiceId,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getInvoiceDetail;
    } catch (error) {
      throw error;
    }
  }
  async requestUnpublishVatInvoice(
    VatInvoiceRequest: VatInvoiceRequestDTO,
    byUser: string
  ) {
    const mutation = REQUEST_UNPUBLISH_VAT_INVOICE;
    const {
      sourceId,
      sourceType,
      signType,
      includeBuyerTaxCode,
      buyerName,
      buyerTaxCode,
      buyerCompany,
      buyerAddress,
      sendMail,
      receiverName,
      receiverEmail,
    } = VatInvoiceRequest;
    const variables = {
      partnerId: this.orgId,
      sourceId,
      sourceType,
      signType,
      includeBuyerTaxCode,
      buyerName,
      buyerTaxCode,
      buyerCompany,
      buyerAddress,
      sendMail,
      receiverName,
      receiverEmail,
      byUser,
    };
    try {
      const response = await this.graphqlMutationV3(mutation, variables);
      return response.requestUnpublishVatInvoice;
    } catch (error) {
      throw error;
    }
  }
  async requestPublishVatInvoice(
    VatInvoiceRequest: VatInvoiceRequestDTO,
    byUser: string
  ) {
    const mutation = REQUEST_PUBLISH_VAT_INVOICE;
    const {
      sourceId,
      sourceType,
      signType,
      includeBuyerTaxCode,
      buyerName,
      buyerTaxCode,
      buyerCompany,
      buyerAddress,
      sendMail,
      receiverName,
      receiverEmail,
    } = VatInvoiceRequest;
    const variables = {
      partnerId: this.orgId,
      sourceId,
      sourceType,
      signType,
      includeBuyerTaxCode,
      buyerName,
      buyerTaxCode,
      buyerCompany,
      buyerAddress,
      sendMail,
      receiverName,
      receiverEmail,
      byUser,
    };
    try {
      const response = await this.graphqlMutationV3(mutation, variables);
      return response.requestPublishVatInvoice;
    } catch (error) {
      throw error;
    }
  }
  async getInvoicesOfOrder(orderId: string) {
    const query = GET_INVOICES_OF_ORDER;
    const variables = {
      orderId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.getInvoicesOfOrder;
    } catch (error) {
      throw error;
    }
  }
  async viewPublishedInvoice(invoiceId: string) {
    const query = VIEW_PUBLISHED_INVOICE;
    const variables = {
      partnerId: this.orgId,
      invoiceId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.viewPublishedInvoice;
    } catch (error) {
      throw error;
    }
  }
  async updateInvoiceItem(
    invoiceId: string,
    updateInvoiceItemDTO: any,
    byUser: string
  ) {
    const mutation = UPDATE_INVOICE_ITEM;
    const variables = {
      partnerId: this.orgId,
      invoiceId,
      updateInvoiceItemDTO,
      byUser,
    };
    try {
      const response = await this.graphqlMutationV3(mutation, variables);
      return response.updateInvoiceItem;
    } catch (error) {
      throw error;
    }
  }
  async getInvoiceItemOfInvoie(invoiceId: string) {
    const query = GET_INVOICE_ITEM_OF_INVOICE;
    const variables = {
      invoiceId,
    };
    try {
      const response = await this.graphqlQueryV3(query, variables);
      return response.getInvoiceItemOfInvoie;
    } catch (error) {
      throw error;
    }
  }
}
