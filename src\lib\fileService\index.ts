import { Service } from "../serviceSDK";

export interface UploadFileRequest {
  uploadFile: File;
  path: string;
  parentId?: string;
  parentType?: string;
}

export interface UploadFileResponse {
  fileName: string;
  filePath: string;
  fileSize: number;
}

export class FileServiceSerVice extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  /**
   * Upload file to the file service
   * @param uploadFile - File object to upload
   * @param path - Directory path to save the file
   * @param parentId - Reference ID of parent object (optional)
   * @param parentType - Type of parent object reference (optional)
   * @returns Promise<UploadFileResponse>
   */
  async uploadFile(
    uploadFile: File,
    path: string,
    parentId?: string,
    parentType?: string
  ): Promise<UploadFileResponse> {
    const endpoint = `/upload`;
    const method: "POST" = "POST";

    try {
      const formData = new FormData();
      formData.append("uploadFile", uploadFile);
      formData.append("path", path);

      if (parentId) {
        formData.append("parentId", parentId);
      }

      if (parentType) {
        formData.append("parentType", parentType);
      }

      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        formData
      );

      return response as UploadFileResponse;
    } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
    }
  }

  /**
   * Upload image file (convenience method)
   * @param imageFile - Image file to upload
   * @param path - Directory path to save the image (default: "public")
   * @param parentId - Reference ID of parent object (optional)
   * @param parentType - Type of parent object reference (optional)
   * @returns Promise<UploadFileResponse>
   */
  async uploadImage(
    imageFile: File,
    path: string = "public",
    parentId?: string,
    parentType?: string
  ): Promise<UploadFileResponse> {
    // Validate that the file is an image
    if (!imageFile.type.startsWith("image/")) {
      throw new Error("File must be an image");
    }

    return this.uploadFile(imageFile, path, parentId, parentType);
  }
}
