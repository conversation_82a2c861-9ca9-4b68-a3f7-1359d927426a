import { Service } from "../serviceSDK";
export class FileServiceSerVice extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async uploadImage(imageCode: string,parentId:string, parentType:string) {
    const endpoint = `/omnichannel/files/upload`;
    const method: "POST" = "POST";
    try {
      const formData = new FormData();
      formData.append("uploadFile", imageCode);
      formData.append("path", "public");
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        formData
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
