# Longvan Storefront JavaScript Client SDK Documentation

[![npm version](https://badge.fury.io/js/%40longvansoftware%2Fstorefront-js-client.svg)](https://badge.fury.io/js/%40longvansoftware%2Fstorefront-js-client)
[![TypeScript](https://img.shields.io/badge/%3C%2F%3E-TypeScript-%230074c1.svg)](http://www.typescriptlang.org/)
[![License: ISC](https://img.shields.io/badge/License-ISC-blue.svg)](https://opensource.org/licenses/ISC)

A comprehensive TypeScript/JavaScript SDK for integrating with the Longvan e-commerce platform. This SDK provides easy-to-use interfaces for managing products, orders, users, payments, and other e-commerce functionalities.

## 🚀 Quick Start

```bash
npm install @longvansoftware/storefront-js-client
```

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

const sdk = new SDK('your-org-id', 'your-store-id', 'dev');
sdk.setToken('your-access-token');

// Ready to use!
const products = await sdk.product.getSimpleProducts({ currentPage: 1, maxResult: 10 });
```

## 📚 Documentation Structure

### Getting Started
- [Installation & Setup](./getting-started/installation.md)
- [Quick Start Guide](./getting-started/quick-start.md)
- [Configuration](./getting-started/configuration.md)
- [Authentication](./getting-started/authentication.md)

### Core Services
- [Authentication Service](./services/auth.md) - User authentication and authorization
- [Product Service](./services/product.md) - Product catalog management
- [Order Service](./services/order.md) - Order lifecycle management
- [User Service](./services/user.md) - Customer and user management
- [Payment Service](./services/payment.md) - Payment processing and invoicing
- [CRM Service](./services/crm.md) - Customer relationship management

### Extended Services
- [Warehouse Service](./services/warehouse.md) - Inventory management
- [Computing Service](./services/computing.md) - Cloud computing services
- [Campaign Service](./services/campaign.md) - Marketing campaigns
- [Image Service](./services/image.md) - Image and media management
- [Upload Service](./services/upload.md) - File upload handling

### Utility Services
- [Portal Service](./services/portal.md) - Administrative portal operations
- [Comhub Service](./services/comhub.md) - Communication hub
- [Omnigateway Service](./services/omnigateway.md) - Omnichannel integration
- [Authorization Service](./services/authorization.md) - Token management
- [ZCA Service](./services/zca.md) - Zalo platform integration

### Advanced Topics
- [Error Handling](./advanced/error-handling.md)
- [TypeScript Support](./advanced/typescript.md)
- [Testing](./advanced/testing.md)
- [Performance Optimization](./advanced/performance.md)
- [Best Practices](./advanced/best-practices.md)

### Examples
- [E-commerce Flow](./examples/ecommerce-flow.md)
- [Authentication Patterns](./examples/auth-patterns.md)
- [Product Management](./examples/product-management.md)
- [Order Processing](./examples/order-processing.md)
- [Payment Integration](./examples/payment-integration.md)

### API Reference
- [Complete API Reference](./api/README.md)
- [Type Definitions](./api/types.md)
- [Interfaces](./api/interfaces.md)
- [Enums](./api/enums.md)

## 🏗️ Architecture Overview

```
SDK
├── Core Services
│   ├── Authentication
│   ├── Product Management
│   ├── Order Management
│   ├── User Management
│   ├── Payment Processing
│   └── CRM
├── Extended Services
│   ├── Warehouse
│   ├── Computing
│   ├── Campaign
│   ├── Image/Upload
│   └── Utility Services
└── Infrastructure
    ├── GraphQL Client
    ├── REST Client
    ├── Error Handling
    └── Type System
```

## 🔧 Key Features

- **🎯 Type-Safe**: Full TypeScript support with comprehensive type definitions
- **🔌 Multi-Protocol**: Supports both GraphQL and REST APIs
- **🔐 Secure**: Built-in authentication and token management
- **⚡ Performance**: Optimized for speed with caching and batching
- **🛠️ Developer-Friendly**: Intuitive API design with excellent IntelliSense
- **📱 Cross-Platform**: Works in Node.js, browsers, and React Native
- **🧪 Well-Tested**: Comprehensive test suite with high coverage
- **📖 Well-Documented**: Extensive documentation with examples

## 🌟 What's New in v2.9.0

- Enhanced TypeScript support with stricter types
- Improved error handling and debugging
- New utility services for better integration
- Performance optimizations
- Updated dependencies and security fixes

## 🤝 Community & Support

- **Documentation**: You're reading it! 📖
- **Issues**: [GitLab Issues](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues)
- **Discussions**: [GitLab Discussions](https://gitlab.longvan.vn/long-van-platform-2.0/website/storefront-js-client/-/issues)
- **Email**: Contact the Longvan development team

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](../LICENSE) file for details.

## 🚀 Contributing

We welcome contributions! Please see our [Contributing Guide](./contributing/README.md) for details.

---

**Ready to get started?** Check out our [Quick Start Guide](./getting-started/quick-start.md) or dive into the [API Reference](./api/README.md).
