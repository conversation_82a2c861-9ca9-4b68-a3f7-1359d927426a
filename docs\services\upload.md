# Upload Service

The Upload Service handles file uploads, document management, media processing, and cloud storage integration for various file types.

## Table of Contents

- [Overview](#overview)
- [File Upload Operations](#file-upload-operations)
- [Document Management](#document-management)
- [Upload Progress & Status](#upload-progress--status)
- [Best Practices](#best-practices)

## Overview

The Upload Service provides comprehensive file handling functionality:

- **Multi-format Support**: Handle images, documents, videos, and other file types
- **Progress Tracking**: Monitor upload progress and status in real-time
- **Validation**: Validate file types, sizes, and content before upload
- **Cloud Integration**: Seamless integration with cloud storage providers
- **Batch Operations**: Support for multiple file uploads simultaneously

## File Upload Operations

### File Upload Data Structure

```typescript
interface UploadRequest {
  file: File | Blob;
  fileName: string;
  category: FileCategory;
  metadata?: FileMetadata;
  options?: UploadOptions;
}

interface UploadResponse {
  id: string;
  fileName: string;
  originalName: string;
  url: string;
  size: number;
  mimeType: string;
  category: FileCategory;
  status: UploadStatus;
  uploadedDate: Date;
  uploadedBy: string;
  metadata?: FileMetadata;
}

interface FileMetadata {
  description?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  expirationDate?: Date;
  isPublic?: boolean;
}

interface UploadOptions {
  maxSize?: number;
  allowedTypes?: string[];
  generateThumbnail?: boolean;
  compress?: boolean;
  quality?: number;
  onProgress?: (progress: UploadProgress) => void;
}

type FileCategory = 
  | 'document'
  | 'image'
  | 'video'
  | 'audio'
  | 'archive'
  | 'other';

type UploadStatus = 
  | 'pending'
  | 'uploading'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number;
  remainingTime?: number;
}
```

### Basic File Upload

```typescript
class UploadManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async uploadFile(uploadRequest: UploadRequest): Promise<UploadResponse> {
    try {
      // Validate file before upload
      const validationResult = await this.validateFile(uploadRequest);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // Start upload process
      const uploadResponse = await this.uploadFileAPI(uploadRequest);
      
      console.log('File uploaded successfully:', {
        id: uploadResponse.id,
        fileName: uploadResponse.fileName,
        size: this.formatFileSize(uploadResponse.size),
        url: uploadResponse.url
      });
      
      return uploadResponse;
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  }

  async uploadMultipleFiles(uploadRequests: UploadRequest[]): Promise<UploadResponse[]> {
    try {
      const uploadPromises = uploadRequests.map(request => 
        this.uploadFile(request).catch(error => ({ error, request }))
      );

      const results = await Promise.allSettled(uploadPromises);
      
      const successful: UploadResponse[] = [];
      const failed: Array<{ error: any; request: UploadRequest }> = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const value = result.value;
          if ('error' in value) {
            failed.push(value);
          } else {
            successful.push(value);
          }
        } else {
          failed.push({ error: result.reason, request: uploadRequests[index] });
        }
      });

      console.log(`Batch upload completed: ${successful.length} successful, ${failed.length} failed`);
      
      if (failed.length > 0) {
        console.warn('Failed uploads:', failed.map(f => f.request.fileName));
      }

      return successful;
    } catch (error) {
      console.error('Batch upload failed:', error);
      throw error;
    }
  }

  async uploadWithProgress(uploadRequest: UploadRequest): Promise<UploadResponse> {
    return new Promise((resolve, reject) => {
      const progressCallback = (progress: UploadProgress) => {
        console.log(`Upload progress: ${progress.percentage}% (${this.formatFileSize(progress.loaded)}/${this.formatFileSize(progress.total)})`);
        
        if (uploadRequest.options?.onProgress) {
          uploadRequest.options.onProgress(progress);
        }
      };

      const requestWithProgress = {
        ...uploadRequest,
        options: {
          ...uploadRequest.options,
          onProgress: progressCallback
        }
      };

      this.uploadFileAPI(requestWithProgress)
        .then(resolve)
        .catch(reject);
    });
  }

  private async validateFile(uploadRequest: UploadRequest): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const { file, options } = uploadRequest;

    // File size validation
    const maxSize = options?.maxSize || 50 * 1024 * 1024; // Default 50MB
    if (file.size > maxSize) {
      errors.push(`File size (${this.formatFileSize(file.size)}) exceeds maximum allowed (${this.formatFileSize(maxSize)})`);
    }

    // File type validation
    const allowedTypes = options?.allowedTypes || this.getDefaultAllowedTypes();
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // File name validation
    if (!uploadRequest.fileName || uploadRequest.fileName.trim().length === 0) {
      errors.push('File name is required');
    }

    // Security validation
    const securityCheck = this.performSecurityValidation(file);
    if (!securityCheck.isSecure) {
      errors.push(...securityCheck.issues);
    }

    // Large file warning
    const warningSize = 10 * 1024 * 1024; // 10MB
    if (file.size > warningSize) {
      warnings.push(`Large file size may result in slower upload times`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  private getDefaultAllowedTypes(): string[] {
    return [
      // Images
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      // Documents
      'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain', 'text/csv',
      // Archives
      'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
      // Videos
      'video/mp4', 'video/avi', 'video/quicktime',
      // Audio
      'audio/mpeg', 'audio/wav', 'audio/ogg'
    ];
  }

  private performSecurityValidation(file: File): { isSecure: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check for executable file extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    const fileName = file.name.toLowerCase();
    
    if (dangerousExtensions.some(ext => fileName.endsWith(ext))) {
      issues.push('Executable files are not allowed for security reasons');
    }

    // Check for script files
    const scriptExtensions = ['.js', '.vbs', '.ps1', '.sh'];
    if (scriptExtensions.some(ext => fileName.endsWith(ext))) {
      issues.push('Script files are not allowed for security reasons');
    }

    return {
      isSecure: issues.length === 0,
      issues
    };
  }

  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  // Placeholder method - would be actual SDK call
  private async uploadFileAPI(uploadRequest: UploadRequest): Promise<UploadResponse> {
    // Simulate upload process
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: 'upload_' + Math.random().toString(36).substr(2, 9),
          fileName: uploadRequest.fileName,
          originalName: uploadRequest.file instanceof File ? uploadRequest.file.name : uploadRequest.fileName,
          url: `https://example.com/uploads/${uploadRequest.fileName}`,
          size: uploadRequest.file.size,
          mimeType: uploadRequest.file.type,
          category: uploadRequest.category,
          status: 'completed',
          uploadedDate: new Date(),
          uploadedBy: 'current-user',
          metadata: uploadRequest.metadata
        });
      }, 1000);
    });
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
```

## Document Management

### Document Operations

```typescript
class DocumentManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async uploadDocument(file: File, metadata: DocumentMetadata): Promise<UploadResponse> {
    try {
      const uploadRequest: UploadRequest = {
        file,
        fileName: file.name,
        category: 'document',
        metadata: {
          ...metadata,
          documentType: this.detectDocumentType(file),
          pageCount: await this.getPageCount(file)
        },
        options: {
          maxSize: 100 * 1024 * 1024, // 100MB for documents
          allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain'
          ],
          generateThumbnail: true
        }
      };

      const uploadManager = new UploadManager(this.sdk);
      const result = await uploadManager.uploadFile(uploadRequest);

      // Generate document preview if supported
      if (this.supportsPreview(file.type)) {
        await this.generateDocumentPreview(result.id);
      }

      return result;
    } catch (error) {
      console.error('Document upload failed:', error);
      throw error;
    }
  }

  async getDocumentById(documentId: string): Promise<UploadResponse> {
    try {
      const document = await this.getDocumentAPI(documentId);
      return document;
    } catch (error) {
      console.error('Failed to fetch document:', error);
      throw error;
    }
  }

  async searchDocuments(query: DocumentSearchQuery): Promise<UploadResponse[]> {
    try {
      const documents = await this.searchDocumentsAPI(query);
      console.log(`Found ${documents.length} documents matching query`);
      return documents;
    } catch (error) {
      console.error('Document search failed:', error);
      throw error;
    }
  }

  async deleteDocument(documentId: string): Promise<void> {
    try {
      await this.deleteDocumentAPI(documentId);
      console.log(`Document ${documentId} deleted successfully`);
    } catch (error) {
      console.error('Failed to delete document:', error);
      throw error;
    }
  }

  private detectDocumentType(file: File): string {
    const typeMap: Record<string, string> = {
      'application/pdf': 'PDF',
      'application/msword': 'Word Document',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
      'application/vnd.ms-excel': 'Excel Spreadsheet',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel Spreadsheet',
      'application/vnd.ms-powerpoint': 'PowerPoint Presentation',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint Presentation',
      'text/plain': 'Text Document',
      'text/csv': 'CSV File'
    };

    return typeMap[file.type] || 'Unknown Document';
  }

  private async getPageCount(file: File): Promise<number | undefined> {
    // This would require a PDF parsing library for accurate page count
    // For now, return undefined for non-PDF files
    if (file.type === 'application/pdf') {
      // Placeholder - would use PDF.js or similar library
      return undefined;
    }
    return undefined;
  }

  private supportsPreview(mimeType: string): boolean {
    const previewableTypes = [
      'application/pdf',
      'text/plain',
      'text/csv',
      'image/jpeg',
      'image/png'
    ];
    return previewableTypes.includes(mimeType);
  }

  private async generateDocumentPreview(documentId: string): Promise<void> {
    // Placeholder for document preview generation
    console.log(`Generating preview for document ${documentId}`);
  }

  // Placeholder methods
  private async getDocumentAPI(documentId: string): Promise<UploadResponse> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async searchDocumentsAPI(query: DocumentSearchQuery): Promise<UploadResponse[]> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }

  private async deleteDocumentAPI(documentId: string): Promise<void> {
    throw new Error('Method not implemented - placeholder for actual SDK call');
  }
}

interface DocumentMetadata extends FileMetadata {
  documentType?: string;
  pageCount?: number;
  author?: string;
  subject?: string;
  keywords?: string[];
  version?: string;
}

interface DocumentSearchQuery {
  keyword?: string;
  documentType?: string;
  author?: string;
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
  page?: number;
  limit?: number;
}
```

## Upload Progress & Status

### Progress Tracking

```typescript
class UploadProgressTracker {
  private uploads: Map<string, UploadProgress> = new Map();
  private callbacks: Map<string, (progress: UploadProgress) => void> = new Map();

  startTracking(uploadId: string, onProgress: (progress: UploadProgress) => void): void {
    this.callbacks.set(uploadId, onProgress);
    this.uploads.set(uploadId, {
      loaded: 0,
      total: 0,
      percentage: 0
    });
  }

  updateProgress(uploadId: string, loaded: number, total: number): void {
    const percentage = total > 0 ? Math.round((loaded / total) * 100) : 0;
    const speed = this.calculateSpeed(uploadId, loaded);
    const remainingTime = this.calculateRemainingTime(loaded, total, speed);

    const progress: UploadProgress = {
      loaded,
      total,
      percentage,
      speed,
      remainingTime
    };

    this.uploads.set(uploadId, progress);
    
    const callback = this.callbacks.get(uploadId);
    if (callback) {
      callback(progress);
    }
  }

  completeUpload(uploadId: string): void {
    const progress: UploadProgress = {
      loaded: this.uploads.get(uploadId)?.total || 0,
      total: this.uploads.get(uploadId)?.total || 0,
      percentage: 100
    };

    this.uploads.set(uploadId, progress);
    
    const callback = this.callbacks.get(uploadId);
    if (callback) {
      callback(progress);
    }

    // Clean up after a delay
    setTimeout(() => {
      this.uploads.delete(uploadId);
      this.callbacks.delete(uploadId);
    }, 5000);
  }

  cancelUpload(uploadId: string): void {
    this.uploads.delete(uploadId);
    this.callbacks.delete(uploadId);
  }

  getProgress(uploadId: string): UploadProgress | undefined {
    return this.uploads.get(uploadId);
  }

  getAllProgress(): Map<string, UploadProgress> {
    return new Map(this.uploads);
  }

  private calculateSpeed(uploadId: string, currentLoaded: number): number {
    // Simplified speed calculation
    // In a real implementation, you'd track timestamps and calculate bytes/second
    const previousProgress = this.uploads.get(uploadId);
    if (!previousProgress) return 0;

    const bytesTransferred = currentLoaded - previousProgress.loaded;
    const timeElapsed = 1; // Assume 1 second for simplification
    
    return bytesTransferred / timeElapsed; // bytes per second
  }

  private calculateRemainingTime(loaded: number, total: number, speed: number): number {
    if (speed <= 0 || loaded >= total) return 0;
    
    const remainingBytes = total - loaded;
    return Math.round(remainingBytes / speed); // seconds
  }
}

// Usage example
class UploadWithProgressExample {
  private progressTracker = new UploadProgressTracker();
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async uploadFileWithProgress(file: File): Promise<UploadResponse> {
    const uploadId = 'upload_' + Math.random().toString(36).substr(2, 9);
    
    // Start progress tracking
    this.progressTracker.startTracking(uploadId, (progress) => {
      console.log(`Upload ${uploadId}: ${progress.percentage}%`);
      
      if (progress.speed) {
        console.log(`Speed: ${this.formatSpeed(progress.speed)}`);
      }
      
      if (progress.remainingTime) {
        console.log(`Remaining: ${this.formatTime(progress.remainingTime)}`);
      }
    });

    try {
      const uploadManager = new UploadManager(this.sdk);
      
      const uploadRequest: UploadRequest = {
        file,
        fileName: file.name,
        category: 'document',
        options: {
          onProgress: (progress) => {
            this.progressTracker.updateProgress(uploadId, progress.loaded, progress.total);
          }
        }
      };

      const result = await uploadManager.uploadFile(uploadRequest);
      this.progressTracker.completeUpload(uploadId);
      
      return result;
    } catch (error) {
      this.progressTracker.cancelUpload(uploadId);
      throw error;
    }
  }

  private formatSpeed(bytesPerSecond: number): string {
    const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
    let speed = bytesPerSecond;
    let unitIndex = 0;

    while (speed >= 1024 && unitIndex < units.length - 1) {
      speed /= 1024;
      unitIndex++;
    }

    return `${speed.toFixed(1)} ${units[unitIndex]}`;
  }

  private formatTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}m ${seconds % 60}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }
}
```

## Best Practices

### 1. File Validation and Security

```typescript
// ✅ Good: Comprehensive file validation
class FileSecurityValidator {
  static async validateFileContent(file: File): Promise<SecurityValidationResult> {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Check file signature (magic bytes)
    const signatureValid = await this.validateFileSignature(file);
    if (!signatureValid) {
      issues.push('File signature does not match declared file type');
    }

    // Check for embedded scripts in documents
    if (this.isDocumentType(file.type)) {
      const hasScripts = await this.checkForEmbeddedScripts(file);
      if (hasScripts) {
        issues.push('Document contains embedded scripts');
      }
    }

    // Scan for malicious patterns
    const malwareCheck = await this.performMalwareCheck(file);
    if (!malwareCheck.isClean) {
      issues.push(...malwareCheck.threats);
    }

    // Check file name for suspicious patterns
    const nameCheck = this.validateFileName(file.name);
    if (!nameCheck.isValid) {
      warnings.push(...nameCheck.warnings);
    }

    return {
      isSecure: issues.length === 0,
      issues,
      warnings
    };
  }

  private static async validateFileSignature(file: File): Promise<boolean> {
    // Read first few bytes to check file signature
    const buffer = await file.slice(0, 16).arrayBuffer();
    const bytes = new Uint8Array(buffer);
    
    // Check common file signatures
    const signatures: Record<string, number[]> = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47],
      'application/pdf': [0x25, 0x50, 0x44, 0x46],
      'application/zip': [0x50, 0x4B, 0x03, 0x04]
    };

    const expectedSignature = signatures[file.type];
    if (!expectedSignature) return true; // Unknown type, skip validation

    return expectedSignature.every((byte, index) => bytes[index] === byte);
  }

  private static isDocumentType(mimeType: string): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    return documentTypes.includes(mimeType);
  }

  private static async checkForEmbeddedScripts(file: File): Promise<boolean> {
    // Simplified check - in production, use proper document parsing
    const text = await file.text();
    const scriptPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i
    ];

    return scriptPatterns.some(pattern => pattern.test(text));
  }

  private static async performMalwareCheck(file: File): Promise<{ isClean: boolean; threats: string[] }> {
    // Placeholder for malware scanning
    // In production, integrate with antivirus API
    return { isClean: true, threats: [] };
  }

  private static validateFileName(fileName: string): { isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];
    
    // Check for suspicious extensions
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif'];
    if (suspiciousExtensions.some(ext => fileName.toLowerCase().endsWith(ext))) {
      warnings.push('File has potentially dangerous extension');
    }

    // Check for double extensions
    const extensionCount = (fileName.match(/\./g) || []).length;
    if (extensionCount > 1) {
      warnings.push('File has multiple extensions');
    }

    // Check for very long names
    if (fileName.length > 255) {
      warnings.push('File name is unusually long');
    }

    return {
      isValid: warnings.length === 0,
      warnings
    };
  }
}

interface SecurityValidationResult {
  isSecure: boolean;
  issues: string[];
  warnings: string[];
}
```

### 2. Upload Optimization

```typescript
// ✅ Good: Optimize upload performance
class UploadOptimizer {
  static async optimizeFileForUpload(file: File): Promise<File> {
    // Compress images before upload
    if (file.type.startsWith('image/')) {
      return await this.compressImage(file);
    }

    // Compress documents if they're too large
    if (file.size > 10 * 1024 * 1024) { // 10MB
      return await this.compressDocument(file);
    }

    return file;
  }

  private static async compressImage(file: File, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions (max 1920px width)
        const maxWidth = 1920;
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;

        // Draw and compress
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            resolve(file);
          }
        }, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  private static async compressDocument(file: File): Promise<File> {
    // Placeholder for document compression
    // In production, you might use libraries like pdf-lib for PDF compression
    console.log('Document compression not implemented');
    return file;
  }
}
```

---

**Next Steps:**
- [Portal Service](./portal.md) - Administrative portal operations
- [Advanced Topics](../advanced/README.md) - Advanced SDK usage patterns
- [Examples](../examples/README.md) - Real-world implementation examples
