# Quick Start Guide

Get up and running with the Longvan Storefront JavaScript Client SDK in minutes.

## Installation

```bash
npm install @longvansoftware/storefront-js-client
```

## Basic Setup

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

// Initialize SDK
const sdk = new SDK('your-org-id', 'your-store-id', 'dev');
```

## Authentication

```typescript
// Login
const loginResponse = await sdk.auth.login({
  username: '<EMAIL>',
  password: 'password123'
});

// Set token for authenticated requests
sdk.setToken(loginResponse.accessToken);
```

## Common Operations

### 1. Browse Products

```typescript
// Get categories
const categories = await sdk.product.getCategory('web', 1);

// Search products
const products = await sdk.product.getSimpleProducts({
  currentPage: 1,
  maxResult: 20,
  keyword: 'laptop'
});

// Get specific product
const product = await sdk.product.getProductById('product-id');
```

### 2. Manage Customers

```typescript
// Search customers
const customers = await sdk.user.searchCustomer({
  keyword: 'john',
  currentPage: 1,
  pageSize: 10
});

// Create customer
const newCustomer = await sdk.user.createCustomerV2({
  name: 'John Doe',
  phone: '0123456789',
  email: '<EMAIL>'
}, 'created-by-user-id');
```

### 3. Create Orders

```typescript
// Create order
const orderData = {
  customer_id: 'customer-123',
  line_items: [
    {
      product_id: 'product-123',
      quantity: 2,
      input_price: 150000
    }
  ],
  shipping_address: {
    name: 'John Doe',
    phone: '0123456789',
    address: '123 Main St',
    province_code: 'HCM',
    district_code: 'Q1',
    ward_code: 'P1'
  }
};

const order = await sdk.order.createOrder(
  orderData,
  'web',
  false,
  'user-id'
);
```

### 4. Process Payments

```typescript
// Get payment methods
const paymentMethods = await sdk.payment.getPaymentMethodOfStoreChannel();

// Generate QR payment
const qrPayment = await sdk.payment.genQrPayment(
  'order-id',
  paymentMethods[0].id
);
```

### 5. Check Inventory

```typescript
// Check single product inventory
const inventory = await sdk.warehouse.getInventory('product-sku', 'warehouse-id');

// Check multiple products (V2)
const products = [
  { productId: 'prod-1', variantId: 'var-1', sku: 'SKU001' }
];
const inventoryV2 = await sdk.warehouseV2.getInventory('warehouse-id', products);
```

## Error Handling

```typescript
try {
  const product = await sdk.product.getProductById('product-id');
} catch (error) {
  if (error.response) {
    // REST API error
    console.error('API Error:', error.response.status, error.response.data);
  } else if (error.graphQLErrors) {
    // GraphQL error
    console.error('GraphQL Errors:', error.graphQLErrors);
  } else {
    // Network error
    console.error('Network Error:', error.message);
  }
}
```

## Environment Configuration

```typescript
// Development
const devSdk = new SDK('org-id', 'store-id', 'dev');

// Production
const prodSdk = new SDK('org-id', 'store-id', 'live');
```

## TypeScript Support

```typescript
import { 
  SDK, 
  LoginRequest, 
  LoginResponse, 
  Product,
  LineItem 
} from '@longvansoftware/storefront-js-client';

// Typed requests
const loginRequest: LoginRequest = {
  username: '<EMAIL>',
  password: 'password123'
};

// Typed responses
const loginResponse: LoginResponse = await sdk.auth.login(loginRequest);
```

## Complete Example

```typescript
import { SDK } from '@longvansoftware/storefront-js-client';

async function ecommerceExample() {
  // Initialize
  const sdk = new SDK('your-org-id', 'your-store-id', 'dev');
  
  try {
    // Login
    const loginResponse = await sdk.auth.login({
      username: '<EMAIL>',
      password: 'password123'
    });
    sdk.setToken(loginResponse.accessToken);
    
    // Browse products
    const products = await sdk.product.getSimpleProducts({
      currentPage: 1,
      maxResult: 10
    });
    
    if (products.length > 0) {
      // Create order
      const order = await sdk.order.createOrder({
        customer_id: loginResponse.partyId,
        line_items: [{
          product_id: products[0].id,
          quantity: 1,
          input_price: products[0].price
        }]
      }, 'web', false, loginResponse.partyId);
      
      // Process payment
      const paymentMethods = await sdk.payment.getPaymentMethodOfStoreChannel();
      const qrPayment = await sdk.payment.genQrPayment(order.id, paymentMethods[0].id);
      
      console.log('Order created:', order.id);
      console.log('Payment QR:', qrPayment);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

ecommerceExample();
```

## Next Steps

- Read the full [API Reference](API_REFERENCE.md)
- Check out the complete [README](README.md)
- Explore the TypeScript types for better development experience
- Set up error handling for production use
- Configure proper authentication flow for your application

## Common Patterns

### Pagination

```typescript
async function getAllProducts() {
  let currentPage = 1;
  const maxResult = 50;
  let allProducts = [];
  
  while (true) {
    const products = await sdk.product.getSimpleProducts({
      currentPage,
      maxResult
    });
    
    if (products.length === 0) break;
    
    allProducts.push(...products);
    currentPage++;
  }
  
  return allProducts;
}
```

### Batch Operations

```typescript
async function processMultipleOrders(orderIds: string[]) {
  const results = await Promise.allSettled(
    orderIds.map(id => sdk.order.getOrderDetail(id))
  );
  
  return results.map((result, index) => ({
    orderId: orderIds[index],
    success: result.status === 'fulfilled',
    data: result.status === 'fulfilled' ? result.value : null,
    error: result.status === 'rejected' ? result.reason : null
  }));
}
```

### Retry Logic

```typescript
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
  throw new Error('Max retries exceeded');
}

// Usage
const product = await withRetry(() => 
  sdk.product.getProductById('product-id')
);
```

For more detailed information, see the [README.md](README.md) and [API_REFERENCE.md](API_REFERENCE.md) files.
