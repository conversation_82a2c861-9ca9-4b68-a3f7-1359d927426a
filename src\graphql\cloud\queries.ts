import { gql } from "graphql-tag";

export const SERVICE_DETAIL = gql`
  query ServiceDetail($serviceId: String) {
    serviceDetail(serviceId: $serviceId) {
      service {
        serviceId
        partnerId
        serviceName
        type
        typeName
        status
        ownerId
        startDate
        endDate
        serviceType
        actionRequest {
          id
          name
          type
          uri
        }
        urlPrivate
        urlPublic
        username
        password
        attrs
      }
      resources {
        type
        name
        total
        unit
        value
        component
      }
      configs {
        configId
        name
        configValue
      }
      extraData {
        title
        content {
          name
          type
          value
          action
        }
      }
    }
  }
`;

export const GET_MAIL_RESOURCE = gql`
  query GetMailResource($serviceId: String!) {
    getMailResource(serviceId: $serviceId) {
      accountTotal
      accountUsed
      accountRemain
      storageTotal
      storageUsed
      storageRemain
      storageRemainUnit
      storageTotalUnit
      storageUsedUnit
      enableAntiVirus
      enableAntiSpam
      attachmentLimitUnit
    }
  }
`;

export const GET_USER_MAIL_HOSTING = gql`
  query GetUserMailResource($serviceId: String!) {
    getUserMailHosting(serviceId: $serviceId) {
      email
      username
      fullName
      storageUsedUnit
      storageUsed
    }
  }
`;

export const SERVICE_TYPE = gql`
  query {
    serviceTypes
  }
`;

export const SEARCH_SERVICE = gql`
  query SearchService($filter: CloudServiceFilterInput) {
    searchService(filter: $filter) {
      total
      offset
      maxResult
      resultList {
        serviceId
        partnerId
        serviceName
        type
        typeName
        status
        ownerId
        startDate
        endDate
        serviceType
        actionRequest {
          id
          name
          type
          uri
        }
        urlPrivate
        urlPublic
        username
        password
        attrs
      }
    }
  }
`;
