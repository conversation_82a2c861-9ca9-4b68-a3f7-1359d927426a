import { gql } from "graphql-tag";

export const GET_PAYMENT_METHOD = gql`
  query GetPaymentMethod($orgId: String!, $storeChannelId: String) {
    getPaymentMethod(orgId: $orgId, storeChannelId: $storeChannelId) {
      id
      code
      name
    }
  }
`;

export const GEN_QR_PAYMENT = gql`
  query GenQRPayment(
    $orgId: String!
    $orderId: String!
    $storeId: String!
    $totalAmount: BigDecimal
  ) {
    genQRPayment(
      orgId: $orgId
      orderId: $orderId
      storeId: $storeId
      totalAmount: $totalAmount
    )
  }
`;
export const GET_PAYMENT_METHOD_OF_STORE_CHANNEL = gql`
  query GetPaymentMethodOfStoreChannel(
    $orgId: String!
    $storeChannelId: String!
  ) {
    getPaymentMethodOfStoreChannel(
      orgId: $orgId
      storeChannelId: $storeChannelId
    ) {
      id
      code
      name
      image
      description
    }
  }
`;
export const GET_INVOICE_DETAIL = gql`
  query GetInvoiceDetail($invoiceId: String!) {
    getInvoiceDetail(invoiceId: $invoiceId) {
      id
      orderId
      type
      partyIdFrom
      partyIdTo
      status
      invoiceDate
      invoiceDueDate
      createdBy
      createdStamp
      paymentId
      originalTotalPrice
      totalVAT
      appliedAmount
    }
  }
`;
export const GET_INVOICES_OF_ORDER = gql`
  query GetInvoicesOfOrder($orderId: String!) {
    getInvoicesOfOrder(orderId: $orderId) {
      id
      orderId
      type
      partyIdFrom
      partyIdTo
      status
      invoiceDate
      invoiceDueDate
      createdBy
      createdStamp
      paymentId
      originalTotalPrice
      totalVAT
      appliedAmount
      attributes {
        name
        value
      }
    }
  }
`;
export const VIEW_PUBLISHED_INVOICE = gql`
  query ViewPublishedInvoice($partnerId: String!, $invoiceId: String!) {
    viewPublishedInvoice(partnerId: $partnerId, invoiceId: $invoiceId) {
      code
      message
      invoiceId
      sourceId
      sourceType
      previewLink
      lookupLink
    }
  }
`;
export const GET_INVOICE_ITEM_OF_INVOICE = gql`
  query GetInvoiceItemOfInvoie($invoiceId: String!) {
    getInvoiceItemOfInvoie(invoiceId: $invoiceId) {
      id
      invoiceId
      type
      partyIdFrom
      partyIdTo
      status
      productParentId
      productId
      productName
      sku
      unitType
      quantity
      amount
      vatRateName
      vatRate
      totalVAT
      totalAmount
      invoiceDate
      note
    }
  }
`;
