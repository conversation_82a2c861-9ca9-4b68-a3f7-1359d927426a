import { Console } from "console";
import sdk from "../../setup";

describe("UserService", () => {
  let userService: any;

  beforeAll(() => {
    userService = sdk.user;
  });

  // it("should get person by party ids", async () => {
  //   const partyIds = ["20.104457"];
  //   const response = await userService.getPersonByPartyIds(partyIds);
  //   console.log(response);
  //   expect(response).toBeDefined();
  // });

  // it("should create a company", async () => {
  //   const payload = { name: "Test Company", address: "123 Test St" };
  //   const createdBy = "admin";
  //   const response = await userService.createCompany(payload, createdBy);
  //   expect(response).toBeDefined();
  //   expect(response.name).toBe(payload.name);
  // });

  // it("should update company info", async () => {
  //   const id = "company123";
  //   const fieldName = "address";
  //   const valueUpdate = "456 New Address";
  //   const updatedBy = "admin";
  //   const response = await userService.updateCompanyInfo(id, fieldName, valueUpdate, updatedBy);
  //   expect(response).toBeDefined();
  //   expect(response.address).toBe(valueUpdate);
  // });

  // it("should get customer by ID", async () => {
  //   const customerId = "20.166140";
  //   const response = await userService.getCustomerById(customerId);
  //   expect(response).toBeDefined();
  //   console.log(response);
  //   expect(response.id).toBe(customerId);
  // });

  // it("should search companies", async () => {
  //   const response = await userService.searchCompany({
  //     keyword: "LONG",
  //     limit: 10,
  //   });
  //   expect(response).toBeDefined();
  //   console.log(response);
  //   expect(response.length).toBeLessThanOrEqual(10);
  // });

  // it("should search customers", async () => {
  //   const response = await userService.searchCustomer({
  //     keyword: "",
  //     type: "",
  //     startCreatedDate: null,
  //     endCreatedDate: null,
  //     memberLevel: "",
  //     partnerId: "FOX",
  //     currentPage: 1,
  //     pageSize: 10,
  //   });
  //   expect(response).toBeDefined();
  //   console.log(response);
  //   expect(response.content.length).toBeLessThanOrEqual(10);
  // });

  // it("should search employees", async () => {
  //   const response = await userService.searchEmployees({
  //     keyword: "",
  //     positionShortName: "sale",
  //   });

  //   expect(response).toBeDefined();
  // });

  // it("should get positions by employee ID", async () => {
  //   const employeeId = "20.166140";
  //   const response = await userService.getPositionsByEmployeeId(employeeId);
  //   console.log(response);
  //   expect(response).toBeDefined();
  // });

  // it("should get store channel IDs by employee ID", async () => {
  //   const response = await userService.getStoreChannelIdsByEmployeeId("20.166140");
  //   console.log(response);
  //   expect(response).toBeDefined();
  // });

  // it("should get employees by store channel ID", async () => {
  //   const response = await userService.getEmployeesByStoreChannelId({
  //     storeChannelId: 1000,
  //   });
  //   expect(response).toBeDefined();
  // });

  // it("should get company by contact info ID", async () => {
  //   const response = await userService.getCompanyByContactInfoId("contact123");
  //   expect(response).toBeDefined();
  // });

  // it("should get contact infos by company ID", async () => {
  //   const response = await userService.getContactInfosByCompanyId("company123");
  //   expect(response).toBeDefined();
  // });
});
