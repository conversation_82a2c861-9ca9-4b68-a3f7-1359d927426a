import { gql } from "graphql-tag";

export const GET_STORE_CHANNEL_BY_EMP_ID_QUERY = gql`
  query GetStoreChannelByEmpId($saleId: String) {
    getStoreChannelByEmpId(saleId: $saleId) {
      id
      createdStamp
      name
      type
      enable
      partyId
      warehouses
      warehouseIdDefault
      enableOrderAbleFuture
      enableOrderNegativeQuantity
      storeEcommerceName
      shippingCompanies
      shippingCompanyIdPrimary
      customerIdPrimary
      paymentMethodIdPrimary
      enableCustomProductPrice
      enablePaymentPartial
      paymentPartialPercent
      productStoreLink
    }
  }
`;
