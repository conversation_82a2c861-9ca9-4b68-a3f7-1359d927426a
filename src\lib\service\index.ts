import { Service } from "../serviceSDK";
import {
  GET_SERVICE_BY_ID,
  GET_SERVICE_BY_TYPE,
  GET_SERVICE_BY_OWNER_ID,
  GET_SERVICE_ACTION,
  GET_SERVICE_TICKETS,
  GET_ACTION_ATTRIBUTE,
} from "../../graphql/service/queries";
import { ServiceRequest } from "../../types/service";
import {
  ADD_ACTION_ATTRIBUTE,
  CREATE_SERVICE,
  CREATE_SERVICE_ACTION,
  CREATE_SERVICE_TICKET,
  DELETE_ATTR_VALUE,
  DELETE_SERVICE,
  UPDATE_ATTR_VALUE,
  UPDATE_FAIL_ACTION_PROCESS_STATUS,
  UPDATE_SERVICE,
  UPDATE_SUCCESS_ACTION_PROCESS_STATUS,
} from "../../graphql/service/mutations";
export class ServiceManagementService extends Service {
  /**
   * Constructs a new ProductService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  /**
   * get service by id .
   * @param serviceId - The id of the service
   */
  async getServiceById(serviceId: string) {
    const query = GET_SERVICE_BY_ID;
    const variables = {
      partnerId: this.orgId,
      serviceId,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getServiceById;
    } catch (error) {
      console.log(`Error in getServiceById: ${error}`);
      throw error;
    }
  }

  /**
   * get service by type
   * @param type - The endpoint URL for the service.
   */

  /**
   * get service by owner id
   * @param ownerId - The endpoint URL for the service.
   */
  async getServiceByOwnerId(ownerId: string) {
    const query = GET_SERVICE_BY_OWNER_ID;
    const variables = {
      partnerId: this.orgId,
      ownerId,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getServiceByOwnerId;
    } catch (error) {
      console.log(`Error in getServiceByOwnerId :${error}`);
    }
  }
  /**
   * get service actions
   * @param serviceId -
   * @param actionType
   * @param updatedBy
   */
  async getServiceActions(serviceId: string) {
    const query = GET_SERVICE_ACTION;
    const variables = {
      serviceId: serviceId,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getServiceActions;
    } catch (error) {
      throw error;
    }
  }
  /**
   * create service
   * @param data
   */
  async createService(data: ServiceRequest) {
    const mutation = CREATE_SERVICE;
    const variables = {
      partnerId: this.orgId,
      createModel: data,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createService;
    } catch (error) {
      throw error;
    }
  }
  /**
   * create service
   * @param data
   * @param serviceId
   * @param updatedBy
   */
  async updateService(
    data: ServiceRequest,
    serviceId: String,
    updatedBy: string
  ) {
    const mutation = UPDATE_SERVICE;
    const variables = {
      partnerId: this.orgId,
      serviceId: serviceId,
      updateModel: data,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateService;
    } catch (error) {
      throw error;
    }
  }
  /**
   * delete service
   * @param serviceId
   * @param deletedBy
   */
  async deleteService(serviceId: string, deletedBy: string) {
    const mutation = DELETE_SERVICE;
    const variables = {
      partnerId: this.orgId,
      serviceId: serviceId,
      deletedBy: deletedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.deleteService;
    } catch (error) {
      throw error;
    }
  }
  /**
   * update attr value
   * @param serviceId
   * @param attrName
   * @param attrValue
   * @param updatedBy
   */
  async updateAttrValue(serviceId: string, attr: JSON, updatedBy: string) {
    const mutation = UPDATE_ATTR_VALUE;
    const variables = {
      partnerId: this.orgId,
      serviceId: serviceId,
      attr: attr,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateAttrValue;
    } catch (error) {
      throw error;
    }
  }
  /**
   * delete attr value
   * @param serviceId
   * @param attrName
   * @param updatedBy
   */
  async deleteAttrValue(
    serviceId: string,
    attrName: string,
    updatedBy: string
  ) {
    const mutation = DELETE_ATTR_VALUE;
    const variables = {
      partnerId: this.orgId,
      serviceId: serviceId,
      attrName: attrName,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.deleteAttrValue;
    } catch (error) {
      throw error;
    }
  }
  /**
   * update success action process status
   * @param serviceId
   * @param actionResult
   * @param updatedBy
   */
  async updateSuccessActionProcessStatus(
    serviceActionId: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_SUCCESS_ACTION_PROCESS_STATUS;
    const variables = {
      serviceActionId: serviceActionId,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * update fail action process status
   * @param serviceId
   * @param actionResult
   * @param updatedBy
   */
  async updateFailActionProcessStatus(
    serviceActionId: string,
    description: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_FAIL_ACTION_PROCESS_STATUS;
    const variables = {
      serviceActionId: serviceActionId,
      description: description,
      updatedBy: updatedBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * Creates a service ticket.
   *
   * @param {string} serviceId - The ID of the service.
   * @param {string} name - The name of the service ticket.
   * @param {string} createdBy - The user who created the service ticket.
   * @param {string} description - The description of the service ticket.
   * @returns {Promise<any>} - A promise that resolves to the created service ticket.
   * @throws {Error} - If an error occurs during the creation of the service ticket.
   */
  async createServiceTicket(
    serviceId: string,
    name: string,
    createdBy: string,
    description: string
  ) {
    const mutation = CREATE_SERVICE_TICKET;
    const variables = {
      serviceId,
      name,
      createdBy,
      description,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createServiceTicket;
    } catch (error) {
      throw error;
    }
  }

  async getServiceTicket(serviceId: string) {
    const query = GET_SERVICE_TICKETS;
    const variables = {
      serviceId,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getServiceTickets;
    } catch (error) {
      throw error;
    }
  }

  async createServiceAction(
    serviceId: string,
    actionType: string,
    attributes: JSON,
    createdBy: string,
  ) {
    const mutation = CREATE_SERVICE_ACTION;
    const variables = {
      serviceId,
      actionType,
      attributes,
      createdBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createServiceAction;
    } catch (error) {
      throw error;
    }
  }
  async addActionAttribute(
    actionId: string,
    attribute: JSON,
    createdBy: string
  ) {
    const mutation = ADD_ACTION_ATTRIBUTE;
    const variables = {
      actionId,
      attribute,
      createdBy,
    };
    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.addActionAttribute;
    } catch (error) {
      throw error;
    }
  }

  async getActionAttribute(actionId: string, attributeName: string) {
    const query = GET_ACTION_ATTRIBUTE;
    const variables = {
      actionId,
      attributeName,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getActionAttribute;
    } catch (error) {
      throw error;
    }
  }
}
