// src/service.ts

import {
  ApolloClient,
  InMemoryCache,
  gql,
  NormalizedCacheObject,
  HttpLink,
} from "@apollo/client";
import axios from "axios";
import { DocumentNode } from "graphql";
import fetch from "cross-fetch";
export class Service {
  protected token: string | null = null;
  protected client: ApolloClient<NormalizedCacheObject>;
  protected orgId: string;
  protected storeId: string;
  protected endpoint: string;

  constructor(endpoint: string, orgId: string, storeId: string) {
    this.client = new ApolloClient({
      link: new HttpLink({ uri: endpoint, fetch }),
      // uri: endpoint,
      cache: new InMemoryCache(),
      defaultOptions: {
        query: {
          fetchPolicy: "network-only",
        },
      },
    });
    this.orgId = orgId;
    this.storeId = storeId;
    this.endpoint = endpoint;
  }
  setToken(token: string) {
    this.token = token;
  }
  setStoreId(storeId: string) {
    this.storeId = storeId;
  }
  setOrgId(orgId: string) {
    this.orgId = orgId;
  }
  // <PERSON><PERSON><PERSON> xử lý lỗi từ Apollo Client
  private handleApolloError(error: any): string {
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      return error.graphQLErrors.map((err: any) => err.message).join(", ");
    }
    if (error.networkError) {
      return `Network Error: ${error.networkError.message}`;
    }
    return error.message || "An unknown error occurred";
  }

  protected async graphqlQuery(query: DocumentNode, variables: any) {
    try {
      const { data, errors } = await this.client.query({
        query: gql`
          ${query}
        `,
        variables,
        context: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            partnerId: this.orgId,
          },
        },
      });
      if (errors && errors.length > 0) {
        throw this.handleApolloError({ graphQLErrors: errors });
      }
      return data;
    } catch (error: any) {
      const errorMessage = this.handleApolloError(error);
      throw errorMessage; // Throw message trực tiếp
    }
  }

  protected async graphqlMutation(mutation: DocumentNode, variables: any) {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: gql`
          ${mutation}
        `,
        variables,
        context: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            partnerId: this.orgId,
          },
        },
      });
      if (errors && errors.length > 0) {
        throw this.handleApolloError({ graphQLErrors: errors });
      }
      return data;
    } catch (error: any) {
      const errorMessage = this.handleApolloError(error);
      throw errorMessage; // Throw message trực tiếp
    }
  }
  protected async restApiCallWithToken(
    path: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any,
    headers?: any
  ) {
    try {
      const modifiedHeaders = {
        ...headers,
        PartnerId: this.orgId,
        Authorization: "Bearer " + this.token,
        "X-Ecomos-Access-Token": this.token,
        "Partner-Id": this.orgId,
      };
      const response = await axios({
        url: this.endpoint + path,
        method,
        data,
        headers: modifiedHeaders,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }
  protected async restApiCallWithNoToken(
    path: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any,
    headers?: any
  ) {
    try {
      const modifiedHeaders = {
        ...headers,
        Partnerid: this.orgId,
      };
      const response = await axios({
        url: this.endpoint + path,
        method,
        data,
        headers: modifiedHeaders,
      });
      return response.data;
    } catch (error) {
      console.log(`Error in restApiCallWithNoToken: ${error}`);
      throw error;
    }
  }
  // call api no header
  protected async restApiCallWithNoHeader(
    path: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any,
    headers?: any
  ) {
    try {
      // const modifiedHeaders = {
      //   ...headers,
      //   Partnerid: this.orgId,
      // };
      const response = await axios({
        url: this.endpoint + path,
        method,
        data,
        // headers: modifiedHeaders,
      });
      return response.data;
    } catch (error) {
      console.log(`Error in restApiCallWithNoToken: ${error}`);
      throw error;
    }
  }
  //

  protected async graphqlQueryV2(query: DocumentNode, variables: any) {
    try {
      const { data, errors } = await this.client.query({
        query: gql`
          ${query}
        `,
        variables,
        context: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            PartnerId: this.orgId,
            Authorization: "Bearer " + this.token,
          },
        },
      });
      if (errors) {
        throw new Error(`GraphQL error! errors: ${errors}`);
      }
      return data;
    } catch (error) {
      console.log(`Error in graphqlQuery: ${error}`);
      throw error;
    }
  }
  protected async graphqlMutationV2(mutation: DocumentNode, variables: any) {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: gql`
          ${mutation}
        `,
        variables,
        context: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            PartnerId: this.orgId,
            Authorization: "Bearer " + this.token,
          },
        },
      });
      if (errors) {
        throw new Error(`GraphQL error! errors: ${errors}`);
      }
      return data;
    } catch (error) {
      throw error;
    }
  }
  protected async graphqlQueryV3(query: DocumentNode, variables: any) {
    try {
      const { data, errors } = await this.client.query({
        query: gql`
          ${query}
        `,
        variables,
        context: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // PartnerId: this.orgId, -> payment bị thừa partnerId -> lỗi
            Authorization: "Bearer " + this.token,
          },
        },
      });
      if (errors) {
        throw new Error(`GraphQL error! errors: ${errors}`);
      }
      return data;
    } catch (error) {
      console.log(`Error in graphqlQuery: ${error}`);
      throw error;
    }
  }
  protected async graphqlMutationV3(mutation: DocumentNode, variables: any) {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: gql`
          ${mutation}
        `,
        variables,
        context: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // PartnerId: this.orgId,
            Authorization: "Bearer " + this.token,
          },
        },
      });
      if (errors) {
        throw new Error(`GraphQL error! errors: ${errors}`);
      }
      return data;
    } catch (error) {
      throw error;
    }
  }
}
