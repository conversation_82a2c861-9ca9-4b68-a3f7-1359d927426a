# Order Service

The Order Service handles the complete order lifecycle from creation to fulfillment, including order management, line items, shipping, and status tracking.

## Table of Contents

- [Overview](#overview)
- [Order Creation](#order-creation)
- [Order Management](#order-management)
- [Line Item Management](#line-item-management)
- [Order Status & Tracking](#order-status--tracking)
- [Shipping Management](#shipping-management)
- [Address Management](#address-management)
- [Order Queries](#order-queries)
- [Best Practices](#best-practices)

## Overview

The Order Service provides comprehensive order management functionality:

- **Order Creation**: Create new orders with line items and shipping information
- **Order Management**: Update, cancel, and track orders
- **Line Item Management**: Add, update, and remove order items
- **Status Tracking**: Monitor order progress through various states
- **Shipping Integration**: Calculate shipping costs and manage delivery
- **Address Management**: Handle shipping and billing addresses

## Order Creation

### Basic Order Creation

Create a new order with products and customer information:

```typescript
async function createBasicOrder() {
  const orderData = {
    customer_id: 'customer-123',
    line_items: [
      {
        product_id: 'product-456',
        quantity: 2,
        input_price: 150000
      },
      {
        product_id: 'product-789',
        quantity: 1,
        input_price: 300000
      }
    ],
    shipping_address: {
      name: '<PERSON>',
      phone: '0123456789',
      address: '123 Main Street, District 1',
      province_code: 'HCM',
      district_code: 'Q1',
      ward_code: 'P1'
    }
  };

  try {
    const order = await sdk.order.createOrder(
      orderData,
      'web',        // platform
      false,        // createDraft (false = create confirmed order)
      'user-id'     // created_by
    );
    
    console.log('Order created successfully:', {
      orderId: order.id,
      status: order.status,
      total: order.total
    });
    
    return order;
  } catch (error) {
    console.error('Order creation failed:', error);
    throw error;
  }
}
```

### Create Draft Order

Create a draft order that can be modified before confirmation:

```typescript
async function createDraftOrder() {
  const orderData = {
    customer_id: 'customer-123',
    line_items: [
      {
        product_id: 'product-456',
        quantity: 1,
        input_price: 150000
      }
    ]
  };

  try {
    const draftOrder = await sdk.order.createOrder(
      orderData,
      'web',
      true,         // createDraft = true
      'user-id'
    );
    
    console.log('Draft order created:', draftOrder.id);
    return draftOrder;
  } catch (error) {
    console.error('Draft order creation failed:', error);
    throw error;
  }
}
```

### Order Data Structure

```typescript
interface OrderData {
  customer_id: string;
  line_items: LineItem[];
  shipping_address?: ShippingAddress;
  billing_address?: BillingAddress;
  notes?: string;
  discount_amount?: number;
  shipping_fee?: number;
  payment_method_id?: string;
}

interface LineItem {
  quantity: number;
  parent_id?: string;           // For product variants
  product_id: string;
  input_price?: number;         // Override product price
  discount_amount?: number;     // Item-specific discount
}

interface ShippingAddress {
  name: string;
  phone: string;
  address: string;
  province_code: string;
  district_code: string;
  ward_code: string;
  address_default?: boolean;
}
```

## Order Management

### Get Order Details

Retrieve complete order information:

```typescript
async function getOrderDetails(orderId: string) {
  try {
    const orderDetail = await sdk.order.getOrderDetail(orderId);
    
    console.log('Order details:', {
      id: orderDetail.id,
      status: orderDetail.status,
      customer: orderDetail.customer,
      items: orderDetail.line_items,
      total: orderDetail.total,
      createdDate: orderDetail.created_date
    });
    
    return orderDetail;
  } catch (error) {
    console.error('Failed to fetch order details:', error);
    throw error;
  }
}
```

### Update Order Status

Change the status of an order:

```typescript
async function updateOrderStatus(
  orderId: string, 
  newStatus: string, 
  updatedBy: string
) {
  try {
    await sdk.order.updateOrderStatus(orderId, newStatus, updatedBy);
    console.log(`Order ${orderId} status updated to ${newStatus}`);
  } catch (error) {
    console.error('Failed to update order status:', error);
    throw error;
  }
}

// Common order statuses
const ORDER_STATUSES = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED'
} as const;
```

### Cancel Order

Cancel an order with reason and notes:

```typescript
async function cancelOrder(orderId: string, reason: string, note: string) {
  const cancelData = {
    reason: 'CUSTOMER' as const,  // or other cancellation reasons
    updatedBy: 'user-id',
    note: note,
    orderType: 'SALES' as const
  };

  try {
    await sdk.order.cancelOrder(orderId, cancelData);
    console.log(`Order ${orderId} cancelled successfully`);
  } catch (error) {
    console.error('Order cancellation failed:', error);
    throw error;
  }
}

// Cancellation reasons
const CANCEL_REASONS = {
  CUSTOMER: 'CUSTOMER',
  INVENTORY: 'INVENTORY',
  PAYMENT: 'PAYMENT',
  FRAUD: 'FRAUD',
  OTHER: 'OTHER'
} as const;
```

## Line Item Management

### Add Line Items to Order

Add new products to an existing order:

```typescript
async function addItemsToOrder(orderId: string) {
  const newLineItems = [
    {
      quantity: 1,
      product_id: 'product-999',
      input_price: 200000,
      discount_amount: 0
    },
    {
      quantity: 2,
      product_id: 'product-888',
      input_price: 100000,
      discount_amount: 10000
    }
  ];

  try {
    const result = await sdk.order.addOrderLineItems(orderId, newLineItems);
    console.log('Line items added successfully:', result);
    return result;
  } catch (error) {
    console.error('Failed to add line items:', error);
    throw error;
  }
}
```

### Line Item Management Helper

```typescript
class OrderLineItemManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async addSingleItem(
    orderId: string, 
    productId: string, 
    quantity: number, 
    price?: number
  ) {
    const lineItem = {
      quantity,
      product_id: productId,
      input_price: price,
      discount_amount: 0
    };

    return await this.sdk.order.addOrderLineItems(orderId, [lineItem]);
  }

  async calculateOrderTotal(lineItems: LineItem[]): Promise<number> {
    let total = 0;
    
    for (const item of lineItems) {
      const itemPrice = item.input_price || 0;
      const itemDiscount = item.discount_amount || 0;
      total += (itemPrice * item.quantity) - itemDiscount;
    }
    
    return total;
  }

  validateLineItems(lineItems: LineItem[]): string[] {
    const errors: string[] = [];
    
    lineItems.forEach((item, index) => {
      if (!item.product_id) {
        errors.push(`Line item ${index + 1}: Product ID is required`);
      }
      if (item.quantity <= 0) {
        errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
      }
      if (item.input_price && item.input_price < 0) {
        errors.push(`Line item ${index + 1}: Price cannot be negative`);
      }
    });
    
    return errors;
  }
}
```

## Order Status & Tracking

### Order Status Workflow

```typescript
class OrderStatusManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async progressOrder(orderId: string, updatedBy: string) {
    try {
      const order = await this.sdk.order.getOrderDetail(orderId);
      const nextStatus = this.getNextStatus(order.status);
      
      if (nextStatus) {
        await this.sdk.order.updateOrderStatus(orderId, nextStatus, updatedBy);
        console.log(`Order ${orderId} progressed from ${order.status} to ${nextStatus}`);
      } else {
        console.log(`Order ${orderId} is already at final status: ${order.status}`);
      }
    } catch (error) {
      console.error('Failed to progress order:', error);
      throw error;
    }
  }

  private getNextStatus(currentStatus: string): string | null {
    const statusFlow = {
      'DRAFT': 'PENDING',
      'PENDING': 'CONFIRMED',
      'CONFIRMED': 'PROCESSING',
      'PROCESSING': 'SHIPPED',
      'SHIPPED': 'DELIVERED'
    };

    return statusFlow[currentStatus as keyof typeof statusFlow] || null;
  }

  canCancelOrder(status: string): boolean {
    const cancellableStatuses = ['DRAFT', 'PENDING', 'CONFIRMED'];
    return cancellableStatuses.includes(status);
  }

  canModifyOrder(status: string): boolean {
    const modifiableStatuses = ['DRAFT', 'PENDING'];
    return modifiableStatuses.includes(status);
  }
}
```

## Shipping Management

### Calculate Shipping Fee

Calculate shipping costs for an order:

```typescript
async function calculateShipping(shippingData: any) {
  try {
    const shippingFee = await sdk.order.calculateShippingFee(shippingData);
    console.log('Shipping fee calculated:', shippingFee);
    return shippingFee;
  } catch (error) {
    console.error('Shipping calculation failed:', error);
    throw error;
  }
}
```

### Get Shipping Services

Retrieve available shipping options:

```typescript
async function getShippingServices(carrierId: string) {
  try {
    const services = await sdk.order.getListShippingService(carrierId);
    console.log('Available shipping services:', services);
    return services;
  } catch (error) {
    console.error('Failed to get shipping services:', error);
    throw error;
  }
}
```

### Shipping Integration Helper

```typescript
class ShippingManager {
  private sdk: SDK;

  constructor(sdk: SDK) {
    this.sdk = sdk;
  }

  async getShippingOptions(
    fromAddress: any, 
    toAddress: any, 
    packageInfo: any
  ) {
    try {
      // Get all available carriers
      const carriers = await this.getAvailableCarriers();
      
      // Calculate shipping for each carrier
      const shippingOptions = await Promise.allSettled(
        carriers.map(async (carrier) => {
          const services = await this.sdk.order.getListShippingService(carrier.id);
          
          const options = await Promise.allSettled(
            services.map(async (service) => {
              const fee = await this.sdk.order.calculateShippingFee({
                carrierId: carrier.id,
                serviceId: service.id,
                fromAddress,
                toAddress,
                packageInfo
              });
              
              return {
                carrier: carrier.name,
                service: service.name,
                fee: fee.amount,
                estimatedDays: fee.estimatedDays
              };
            })
          );
          
          return options
            .filter(result => result.status === 'fulfilled')
            .map(result => (result as PromiseFulfilledResult<any>).value);
        })
      );

      return shippingOptions
        .filter(result => result.status === 'fulfilled')
        .flatMap(result => (result as PromiseFulfilledResult<any>).value);
        
    } catch (error) {
      console.error('Failed to get shipping options:', error);
      throw error;
    }
  }

  private async getAvailableCarriers() {
    // This would typically come from a configuration or API
    return [
      { id: 'ghn', name: 'Giao Hàng Nhanh' },
      { id: 'ghtk', name: 'Giao Hàng Tiết Kiệm' },
      { id: 'vnpost', name: 'VN Post' }
    ];
  }
}
```

## Address Management

### Create Receiver Information

Store shipping address information:

```typescript
async function createReceiverInfo(ownerId: string, address: ShippingAddress) {
  try {
    const result = await sdk.order.createInfoReceiver(ownerId, address);
    console.log('Receiver information created:', result);
    return result;
  } catch (error) {
    console.error('Failed to create receiver info:', error);
    throw error;
  }
}
```

### Get Receiver Information

Retrieve stored shipping addresses:

```typescript
async function getReceiverInfo(ownerId: string) {
  try {
    const receiverInfo = await sdk.order.getInfoReceiver(ownerId);
    console.log('Receiver information:', receiverInfo);
    return receiverInfo;
  } catch (error) {
    console.error('Failed to get receiver info:', error);
    throw error;
  }
}
```

### Update Customer Information

Update customer information for orders in front-end context:

```typescript
async function updateCustomerInfo(
  partnerId: string,
  orderId: string,
  customerInfo: CustomerInfo
) {
  try {
    const result = await sdk.order.updateCustomerInfoFront(
      partnerId,
      orderId,
      customerInfo
    );
    console.log('Customer information updated:', result);
    return result;
  } catch (error) {
    console.error('Failed to update customer info:', error);
    throw error;
  }
}

// Example usage
const customerInfo = {
  id: "customer123",
  email: "<EMAIL>",
  name: "John Doe",
  phone: "+1234567890"
};

await updateCustomerInfo("partner123", "order456", customerInfo);
```

## Order Queries

### Get Orders with Filters

Query orders with various filters:

```typescript
async function getOrdersWithFilters() {
  const query = {
    customerId: 'customer-123',
    status: ['CONFIRMED', 'PROCESSING'],
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    currentPage: 1,
    maxResult: 20
  };

  try {
    const orders = await sdk.order.getOrders(query);
    console.log('Filtered orders:', orders);
    return orders;
  } catch (error) {
    console.error('Order query failed:', error);
    throw error;
  }
}
```

### Get Customer Orders

Retrieve all orders for a specific customer:

```typescript
async function getCustomerOrders(customerId: string) {
  try {
    const orders = await sdk.order.getOrdersByCustomerId(
      customerId,
      1,    // currentPage
      50    // maxResult
    );
    
    console.log(`Found ${orders.length} orders for customer ${customerId}`);
    return orders;
  } catch (error) {
    console.error('Failed to get customer orders:', error);
    throw error;
  }
}
```

### Get Orders by Status

Retrieve orders filtered by status:

```typescript
async function getOrdersByStatus(statuses: string[]) {
  try {
    const orders = await sdk.order.getOrdersByStatus(
      statuses,
      1,    // currentPage
      100   // maxResult
    );
    
    console.log(`Found ${orders.length} orders with status: ${statuses.join(', ')}`);
    return orders;
  } catch (error) {
    console.error('Failed to get orders by status:', error);
    throw error;
  }
}
```

## Best Practices

### 1. Order Validation

```typescript
// ✅ Good: Validate order data before creation
function validateOrderData(orderData: OrderData): string[] {
  const errors: string[] = [];
  
  if (!orderData.customer_id) {
    errors.push('Customer ID is required');
  }
  
  if (!orderData.line_items || orderData.line_items.length === 0) {
    errors.push('At least one line item is required');
  }
  
  orderData.line_items?.forEach((item, index) => {
    if (!item.product_id) {
      errors.push(`Line item ${index + 1}: Product ID is required`);
    }
    if (item.quantity <= 0) {
      errors.push(`Line item ${index + 1}: Quantity must be positive`);
    }
  });
  
  return errors;
}
```

### 2. Error Recovery

```typescript
// ✅ Good: Implement retry logic for order operations
async function createOrderWithRetry(orderData: OrderData, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await sdk.order.createOrder(orderData, 'web', false, 'user-id');
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. Status Management

```typescript
// ✅ Good: Use constants for order statuses
const ORDER_STATUS = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
} as const;

type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];
```

---

**Next Steps:**
- [Payment Service](./payment.md) - Payment processing and invoicing
- [User Service](./user.md) - Customer management
- [CRM Service](./crm.md) - Customer relationship management
