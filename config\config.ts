export const environmentEndpoints = {
  dev: {
    product: "https://product-service.dev.longvan.vn/product-service/graphql",
    crm: "https://crm-ticket-gateway.dev.longvan.vn/crm-graph-gateway/graphql",
    auth: "https://crm.dev.longvan.vn/authorization-gateway/graphql",
    order: "https://storefront.dev.longvan.vn/v2",
    user: "https://user.dev.longvan.vn/user-gateway/graphql",
    payment: "https://portal.dev.longvan.vn/invoice-gateway/graphql",
    service: "https://api-gateway.dev.longvan.vn/service-api/graphql",
    warehouse:
      "https://portal.dev.longvan.vn/facility-api/public-facility/1.0.0/inventory-item",
    computing: "https://api-gateway.dev.longvan.vn/computing-service/graphql",
    campaign: "https://crm.dev.longvan.vn/campaign-gateway/graphql",
    image: "https://product-service.dev.longvan.vn/product-service/v1/products",
    paymentV2: "https://payment.dev.longvan.vn/graphql",
    warehouseV2: "https://portal.dev.longvan.vn/facility-api",
    deepLinkVietQr: "https://api.vietqr.io/v2",
    comhub: "https://com-hub.dev.longvan.vn/com-hub/v1",
    portal: "https://portal.dev.longvan.vn",
    upload: "https://fileservice.dev.longvan.vn/omnichannel/files/upload",
    getImage: "https://s3-img-gw.longvan.net/img/omnichannel",
    accounting: "https://api-gateway.dev.longvan.vn/accounting-service",
    omnigateway: "https://omni-gateway.dev.longvan.vn/omni-gateway/v1",
    authorization: "https://id.dev.longvan.vn/authorization/public",
    zca: "https://zca.dev.longvan.vn",
    cashbook:'https://api-gateway.dev.longvan.vn/cashbook-service/graphql',
    store: "https://storefront.dev.longvan.vn/v2",
    fileService:"https://fileservice.dev.longvan.vn"
  },
  live: {
    product: "https://product-service.longvan.vn/product-service/graphql",
    crm: "https://crm-ticket-gateway.longvan.vn/crm-graph-gateway/graphql",
    auth: "https://crm.longvan.vn/authorization-gateway/graphql",
    order: "https://storefront.longvan.vn/v2",
    user: "https://user.longvan.vn/user-gateway/graphql",
    payment: "https://portal.longvan.vn/invoice-gateway/graphql",
    service: "https://api-gateway.longvan.vn/service-api/graphql",
    warehouse:
      "https://portal.longvan.vn/facility-api/public-facility/1.0.0/inventory-item",
    computing: "https://api-gateway.longvan.vn/computing-service/graphql",
    campaign: "https://crm.longvan.vn/campaign-gateway/graphql",
    image: "https://product-service.longvan.vn/product-service/v1/products",
    paymentV2: "https://payment.longvan.vn/graphql",
    warehouseV2: "https://portal.longvan.vn/facility-api",
    deepLinkVietQr: "https://api.vietqr.io/v2",
    comhub: "https://com-hub.longvan.vn/com-hub/v1",
    portal: "	https://portal.longvan.vn",
    upload: "https://fileservice.longvan.vn/longvan/files/upload",
    getImage: "https://s3-hcm-r1.longvan.net/files-store-longvan",
    accounting: "https://api-gateway.dev.longvan.vn/accounting-service",
    omnigateway: "https://omni-gateway.longvan.vn/omni-gateway/v1",
    authorization: "https://id.longvan.vn/authorization/public",
    zca: "https://zca.longvan.vn",
    cashbook:'https://api-gateway.dev.longvan.vn/cashbook-service/graphql',
    store: "https://storefront.longvan.vn/v2",
    fileService:"https://fileservice.longvan.vn"
  },
};
