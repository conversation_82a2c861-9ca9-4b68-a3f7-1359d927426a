import {
  CREATE_PORT_NAT,
  CREATE_SNAP_SHOT,
  DELETE_SNAPSHOT,
  POWER_OFF,
  POWER_ON,
  REMOVE_PORT_NAT,
  RESTARTVM,
  ROLLBACK_SNAPSHOT,
  UPDATE_DESCRIPTION_PORTNAT,
  UPDATE_PORT_NAT,
} from "../../graphql/computing/mutations";
import {
  COMPUTING_DETAIL,
  PORTNATS,
  SNAP_SHOTS,
} from "../../graphql/computing/queries";
import {
  CreatePortNat,
  CreateSnapShot,
  RollBackSnapShot,
  UpdateDescriptionPortNat,
} from "../../types/computing";
import { Service } from "../serviceSDK";

export class ComputingService extends Service {
  /**
   * Constructs a new OrderService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }

  async computingDetail(computingId: string) {
    const query = COMPUTING_DETAIL;
    const variables = {
      computingId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.computingDetail;
    } catch (error) {
      console.log(`Error in computingDetail: ${error}`);
      throw error;
    }
  }

  async portNats(computingId: string) {
    const query = PORTNATS;
    const variables = {
      computingId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.portNats;
    } catch (error) {
      console.log(`Error in portNats: ${error}`);
      throw error;
    }
  }

  async restartVM(computingId: string, actor: string) {
    const mutation = RESTARTVM;
    const variables = {
      computingId,
      actor,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.restart;
    } catch (error) {
      console.log(`Error in restartVM: ${error}`);
      throw error;
    }
  }

  async updateDescriptionPortNat(
    payload: UpdateDescriptionPortNat,
    updateBy: string
  ) {
    const mutation = UPDATE_DESCRIPTION_PORTNAT;
    const variables = {
      portNatId: payload.portNatId,
      description: payload.description,
      updateBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updateDescriptionPortNat;
    } catch (error) {
      console.log(`Error in updateDescriptionPortNat: ${error}`);
      throw error;
    }
  }

  async createPortNat(payload: CreatePortNat, createBy: string) {
    const mutation = CREATE_PORT_NAT;
    const variables = {
      portNatId: payload.portNatId,
      translatedPort: payload.translatedPort,
      createBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createPortNat;
    } catch (error) {
      console.log(`Error in createPortNat: ${error}`);
      throw error;
    }
  }

  async updatePortNat(payload: CreatePortNat, updateBy: string) {
    const mutation = UPDATE_PORT_NAT;
    const variables = {
      portNatId: payload.portNatId,
      translatedPort: payload.translatedPort,
      updateBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.updatePortNat;
    } catch (error) {
      console.log(`Error in updatePortNat: ${error}`);
      throw error;
    }
  }

  async removePortNat(portNatId: string, updateBy: string) {
    const mutation = REMOVE_PORT_NAT;
    const variables = {
      portNatId,
      updateBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.removePortNat;
    } catch (error) {
      console.log(`Error in removePortNat: ${error}`);
      throw error;
    }
  }

  async startVM(computingId: string, actor: string) {
    const mutation = POWER_ON;
    const variables = {
      computingId,
      actor,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.powerOn;
    } catch (error) {
      console.log(`Error in startVM: ${error}`);
      throw error;
    }
  }

  async stopVM(computingId: string, actor: string) {
    const mutation = POWER_OFF;
    const variables = {
      computingId,
      actor,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.powerOff;
    } catch (error) {
      console.log(`Error in stopVM: ${error}`);
      throw error;
    }
  }

  async snapshots(computingId: string) {
    const query = SNAP_SHOTS;
    const variables = {
      computingId,
    };

    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.snapshots;
    } catch (error) {
      console.log(`Error in snapshots: ${error}`);
      throw error;
    }
  }

  async createSnapshot(payload: CreateSnapShot, createBy: string) {
    const mutation = CREATE_SNAP_SHOT;
    const variables = {
      computingId: payload.computingId,
      snapshotName: payload.snapshotName,
      createBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.createSnapshot;
    } catch (error) {
      console.log(`Error in createSnapshot: ${error}`);
      throw error;
    }
  }
  
  async rollbackSnapshot(payload: RollBackSnapShot, createBy: string) {
    const mutation = ROLLBACK_SNAPSHOT;
    const variables = {
      computingId: payload.computingId,
      snapshotId: payload.snapshotId,
      createBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.rollbackSnapshot;
    } catch (error) {
      console.log(`Error in rollbackSnapshot: ${error}`);
      throw error;
    }
  }

  async deleteSnapshot(payload: RollBackSnapShot, updateBy: string) {
    const mutation = DELETE_SNAPSHOT;
    const variables = {
      computingId: payload.computingId,
      snapshotId: payload.snapshotId,
      updateBy,
    };

    try {
      const response = await this.graphqlMutationV2(mutation, variables);
      return response.deleteSnapshot;
    } catch (error) {
      console.log(`Error in deleteSnapshot: ${error}`);
      throw error;
    }
  }
}
