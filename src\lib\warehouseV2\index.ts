import { Service } from "../serviceSDK";

export class WarehouseServiceV2 extends Service {
  /**
   * Constructs a new OrderService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }
  async getInventory(warehouseId: string, data: any[]) {
    const endpoint = `/public-facility/1.0.0/product-inventory-v3/${warehouseId}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithNoToken(
        endpoint,
        method,
        data
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  async getInfoWarehouse(warehouseId: string) {
    try {
      const endpoint = `/facility/1.0.0/facility/${warehouseId}`;
      const method: "GET" = "GET";
      const response = await this.restApiCallWithNoToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
