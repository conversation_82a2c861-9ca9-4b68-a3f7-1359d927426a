export interface Product {
  id: string;
  title: string;
  description: string;
  sku: string;
  price: number;
  available: boolean;
  categories: Category[];
  featuredImage: string;
  subType: string;
}

export interface Category {
  id: string;
  title: string;
  image: string;
  icon: string;
  parentId: string | null;
  level: number;
  sequence: number;
  handle: string;
}

export interface ProductFilterOptions {
  partnerId?: string;
  storeChannel?: string;
  category?: string;
  product?: string;
  sku?: string;
  tag?: string;
  priceFrom?: number;
  priceTo?: number;
  status?: string;
  productType?: string;
  subType?: string;
  brandId?: string;
  keyword?: string;
  display?: boolean;
  onlyPromotion?: boolean;
  currentPage?: number;
  maxResult?: number;
}
export interface CategoryFilterOptions {
  partnerId?: string;
  storeChannel?: string;
  typeBuild?: string;
  level?: number;
}

export interface Category {
  id: string;
  title: string;
  image: string;
  icon: string;
  parentId: string | null;
  level: number;
  sequence: number;
  handle: string;
  child?: Category[];
}
export interface Brand {
  id: string;
  name: string;
  image: string;
  imageIcon: string;
}
