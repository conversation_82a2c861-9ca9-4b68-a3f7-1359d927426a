import { Service } from "../serviceSDK";
export class AccountingService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async getCustomerWallet(customerId: string, type: string) {
    const endpoint = `/${this.orgId}/get_gl_account?partyId=${customerId}&type=${type}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
