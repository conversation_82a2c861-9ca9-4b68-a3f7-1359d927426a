import { Service } from "../serviceSDK";
export class OmnigatewayService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  async requestJoinRoom(roomId: string, listUser: [string]) {
    const endpoint = `/topics/room/${roomId}/join`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        listUser
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  async getTemplate(type: string) {
    const endpoint = `/message-templates/partner/${this.orgId}/type/${type}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async getInfoChatApp() {
    const endpoint = `/channels/partner/${this.orgId}/social`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
